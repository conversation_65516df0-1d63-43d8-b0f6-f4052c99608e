"""
<PERSON><PERSON><PERSON> to initialize MongoDB for the journal application
Run this script after setting up MongoDB to create necessary collections and indexes
"""

import os
import sys
import django
from pymongo import MongoClient, ASCENDING, DESCENDING
from datetime import datetime

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.conf import settings

def init_mongodb():
    """Initialize MongoDB collections and indexes"""
    print("Initializing MongoDB for journal application...")
    
    # Connect to MongoDB
    client = MongoClient(
        host=settings.DATABASES['default']['CLIENT']['host'],
        port=settings.DATABASES['default']['CLIENT']['port'],
        username=settings.DATABASES['default']['CLIENT'].get('username', ''),
        password=settings.DATABASES['default']['CLIENT'].get('password', ''),
        authSource=settings.DATABASES['default']['CLIENT'].get('authSource', 'admin')
    )
    
    # Get database
    db_name = settings.DATABASES['default']['NAME']
    db = client[db_name]
    
    # Create collections if they don't exist
    collections = [
        'users',
        'journal_entries',
        'journal_streaks',
        'journal_reminders',
    ]
    
    for collection_name in collections:
        if collection_name not in db.list_collection_names():
            print(f"Creating collection: {collection_name}")
            db.create_collection(collection_name)
    
    # Create indexes
    print("Creating indexes...")
    
    # Users collection
    db.users.create_index([("django_id", ASCENDING)], unique=True)
    db.users.create_index([("username", ASCENDING)], unique=True)
    
    # Journal entries collection
    db.journal_entries.create_index([("user_id", ASCENDING)])
    db.journal_entries.create_index([("created_at", DESCENDING)])
    db.journal_entries.create_index([("user_id", ASCENDING), ("created_at", DESCENDING)])
    db.journal_entries.create_index([("tags", ASCENDING)])
    
    # Journal streaks collection
    db.journal_streaks.create_index([("user_id", ASCENDING)], unique=True)
    
    # Journal reminders collection
    db.journal_reminders.create_index([("user_id", ASCENDING)])
    
    print("MongoDB initialization complete!")
    
    # Return the database object for further operations
    return db

if __name__ == "__main__":
    db = init_mongodb()
    
    # Check if there are any users in the database
    users_count = db.users.count_documents({})
    if users_count == 0:
        print("No users found. You can create users through the application.")
    else:
        print(f"Found {users_count} existing users in the database.")
    
    # Check journal entries
    entries_count = db.journal_entries.count_documents({})
    print(f"Found {entries_count} journal entries in the database.")
    
    print("\nMongoDB setup is complete. You can now run the application.")

