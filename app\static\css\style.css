:root {
  /* Professional color palette */
  --dark-blue: #1a2b3c;
  --slate-gray: #3d4f5c;
  --soft-cream: #f5f1e6;
  --accent-teal: #26a69a;
  --accent-gold: #ffc107;
  
  /* Typography */
  --font-primary: '<PERSON><PERSON>', 'Segoe UI', sans-serif;
  --font-heading: 'Poppins', 'Segoe UI', sans-serif;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2.5rem;
}

/* Base styles */
body {
  background-color: var(--dark-blue);
  color: var(--soft-cream);
  font-family: var(--font-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* Typography */
h1, h2, h3, .entry-title {
  font-family: var(--font-heading);
  color: var(--accent-teal);
  font-weight: 600;
  letter-spacing: -0.02em;
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-md);
}

/* Cards */
.card, .auth-card, .entry-card {
  background-color: var(--slate-gray);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border-left: 4px solid var(--accent-teal);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover, .entry-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Buttons */
.button {
  background: var(--accent-teal);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  font-size: 1rem;
}

.button:hover {
  background: #2bbbad;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(38, 166, 154, 0.3);
}

.pulse-button {
  background: var(--accent-gold);
  color: var(--dark-blue);
  font-weight: 600;
}

.pulse-button:hover {
  background: #ffca28;
  box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
}

/* Layout */
header, footer {
  background-color: rgba(26, 43, 60, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(38, 166, 154, 0.2);
  padding: var(--spacing-md) 0;
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

/* Hero section */
.hero-section {
  background: linear-gradient(135deg, var(--dark-blue), #0f1a24);
  padding: var(--spacing-xl) 0;
  border-bottom: 1px solid rgba(38, 166, 154, 0.2);
  margin-bottom: var(--spacing-xl);
}

.hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-content h1 {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  color: var(--accent-teal);
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-lg);
  color: var(--soft-cream);
  opacity: 0.9;
}

/* Navigation */
.nav-links {
  display: flex;
  gap: var(--spacing-md);
}

.nav-links a {
  color: var(--soft-cream);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 4px;
}

.nav-links a:hover {
  color: var(--accent-teal);
}

/* Animations */
.animated-icon {
  transition: transform 0.3s ease;
}

a:hover .animated-icon, 
button:hover .animated-icon {
  transform: translateY(-2px);
}

/* Form elements */
input, textarea, select {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 0.75rem;
  color: var(--soft-cream);
  width: 100%;
  margin-bottom: var(--spacing-md);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--accent-teal);
  box-shadow: 0 0 0 2px rgba(38, 166, 154, 0.2);
}

/* Responsive */
@media (max-width: 768px) {
  h1 {
    font-size: 2rem;
  }
  
  .hero-content h1 {
    font-size: 2.2rem;
  }
  
  .container {
    width: 95%;
    padding: var(--spacing-sm);
  }
}

/* Calendar styles - fixed grid layout */
.classic-calendar {
  max-width: 600px;
  margin: 0 auto 30px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  border: 4px solid #333;
  background: #fff;
}

.calendar-header {
  background: #00a0e4;
  color: #fff;
  padding: 15px 10px;
  text-align: center;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 3px solid #333;
}

.calendar-header h3 {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.calendar-binding {
  position: absolute;
  top: -15px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 0 30%;
}

.binding-hole {
  width: 25px;
  height: 35px;
  background: #ddd;
  border: 3px solid #333;
  border-radius: 12px;
}

.calendar-nav-btn {
  background: transparent;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s;
}

.calendar-nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.calendar-body {
  background: #f5f5f5;
  padding: 0;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #00a0e4;
  color: #fff;
  font-weight: bold;
  text-align: center;
  border-bottom: 2px solid #333;
}

.weekday {
  padding: 10px 0;
  font-size: 0.9rem;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
}

.weekday:last-child {
  border-right: none;
}

.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-gap: 0;
  background: #fff;
}

.day {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background 0.2s;
  border: 1px solid #ddd;
  font-weight: 500;
}

.day:hover {
  background: #f0f0f0;
}

.day.today {
  background: #e6f7ff;
  font-weight: bold;
  color: #00a0e4;
  border: 2px solid #00a0e4;
}

.day.has-entries {
  position: relative;
}

.day.has-entries::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: #00a0e4;
  border-radius: 50%;
}

.day.other-month {
  color: #aaa;
  background: #f8f8f8;
}

/* Calendar actions */
.calendar-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

/* Dark theme overrides */
[data-theme="dark"] .classic-calendar {
  border: 4px solid #555;
  background: #333;
}

[data-theme="dark"] .calendar-header {
  background: #0077aa;
}

[data-theme="dark"] .binding-hole {
  background: #777;
  border-color: #555;
}

[data-theme="dark"] .calendar-body {
  background: #222;
}

[data-theme="dark"] .weekdays {
  background: #0077aa;
  border-color: #555;
}

[data-theme="dark"] .day {
  background: #444;
  color: #eee;
  border-color: #333;
}

[data-theme="dark"] .day:hover {
  background: #555;
}

[data-theme="dark"] .day.today {
  background: #005577;
  color: #fff;
  border-color: #0099cc;
}

[data-theme="dark"] .day.other-month {
  color: #777;
  background: #3a3a3a;
}

[data-theme="dark"] .day.has-entries::after {
  background: #0099cc;
}

/* Journal by date styles for dark theme */
[data-theme="dark"] .journal-header h2 {
    color: var(--bright-orange);
}

[data-theme="dark"] .entry-card {
    background: var(--dark-brown);
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    border: 1px solid var(--bright-orange);
}

[data-theme="dark"] .entry-title {
    color: var(--bright-orange);
}

[data-theme="dark"] .entry-date {
    color: var(--light-beige);
}

[data-theme="dark"] .entry-preview {
    color: var(--light-beige);
}

[data-theme="dark"] .tag {
    background: var(--dark-purple);
    color: var(--light-beige);
    border: 1px solid var(--bright-orange);
}

[data-theme="dark"] .empty-state {
    background: var(--dark-brown);
    border: 1px solid var(--bright-orange);
}

[data-theme="dark"] .empty-state h3 {
    color: var(--bright-orange);
}

[data-theme="dark"] .empty-state p {
    color: var(--light-beige);
}

[data-theme="dark"] .empty-icon {
    color: var(--bright-orange);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .calendar-header h3 {
    font-size: 1.4rem;
  }
  
  .day {
    font-size: 1rem;
    height: 40px;
  }
  
  .binding-hole {
    width: 20px;
    height: 30px;
  }
}










