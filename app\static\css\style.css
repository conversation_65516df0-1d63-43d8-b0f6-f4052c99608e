:root {
  /* Professional color palette */
  --dark-blue: #1a2b3c;
  --slate-gray: #3d4f5c;
  --soft-cream: #f5f1e6;
  --accent-teal: #26a69a;
  --accent-gold: #ffc107;

  /* Typography */
  --font-primary: '<PERSON><PERSON>', 'Segoe UI', sans-serif;
  --font-heading: 'Poppins', 'Segoe UI', sans-serif;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2.5rem;
}

/* Base styles */
body {
  background-color: var(--dark-blue);
  color: var(--soft-cream);
  font-family: var(--font-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* Typography */
h1, h2, h3, .entry-title {
  font-family: var(--font-heading);
  color: var(--accent-teal);
  font-weight: 600;
  letter-spacing: -0.02em;
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-md);
}

/* Cards */
.card, .auth-card, .entry-card {
  background-color: var(--slate-gray);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border-left: 4px solid var(--accent-teal);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover, .entry-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Buttons */
.button {
  background: var(--accent-teal);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  font-size: 1rem;
}

.button:hover {
  background: #2bbbad;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(38, 166, 154, 0.3);
}

.pulse-button {
  background: var(--accent-gold);
  color: var(--dark-blue);
  font-weight: 600;
}

.pulse-button:hover {
  background: #ffca28;
  box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
}

/* Layout */
header, footer {
  background-color: rgba(26, 43, 60, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(38, 166, 154, 0.2);
  padding: var(--spacing-md) 0;
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

/* Hero section */
.hero-section {
  background: linear-gradient(135deg, var(--dark-blue), #0f1a24);
  padding: var(--spacing-xl) 0;
  border-bottom: 1px solid rgba(38, 166, 154, 0.2);
  margin-bottom: var(--spacing-xl);
}

.hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-content h1 {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  color: var(--accent-teal);
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-lg);
  color: var(--soft-cream);
  opacity: 0.9;
}

/* Navigation */
.nav-links {
  display: flex;
  gap: var(--spacing-md);
}

.nav-links a {
  color: var(--soft-cream);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 4px;
}

.nav-links a:hover {
  color: var(--accent-teal);
}

/* Animations */
.animated-icon {
  transition: transform 0.3s ease;
}

a:hover .animated-icon,
button:hover .animated-icon {
  transform: translateY(-2px);
}

/* Form elements */
input, textarea, select {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 0.75rem;
  color: var(--soft-cream);
  width: 100%;
  margin-bottom: var(--spacing-md);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--accent-teal);
  box-shadow: 0 0 0 2px rgba(38, 166, 154, 0.2);
}

/* Responsive */
@media (max-width: 768px) {
  h1 {
    font-size: 2rem;
  }

  .hero-content h1 {
    font-size: 2.2rem;
  }

  .container {
    width: 95%;
    padding: var(--spacing-sm);
  }
}

/* Modern Calendar Page Layout */
.modern-calendar-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: var(--light-beige);
  min-height: 100vh;
}

/* Calendar Header Section */
.calendar-header-section {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.calendar-title-area {
  text-align: center;
  margin-bottom: 30px;
}

.calendar-title {
  font-size: 2.5rem;
  color: var(--dark-brown);
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.calendar-title i {
  color: var(--accent-teal);
}

.calendar-subtitle {
  color: var(--dark-brown);
  opacity: 0.7;
  font-size: 1.1rem;
  margin: 0;
}

.calendar-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: linear-gradient(135deg, var(--accent-teal), #26a69a);
  color: white;
  padding: 25px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(38, 166, 154, 0.3);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.9;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Calendar Controls Section */
.calendar-controls-section {
  background: white;
  border-radius: 16px;
  padding: 20px 30px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.view-controls {
  display: flex;
  align-items: center;
}

.view-buttons {
  display: flex;
  background: var(--light-beige);
  border-radius: 10px;
  padding: 4px;
  gap: 2px;
}

.view-btn {
  padding: 10px 20px;
  border: none;
  background: transparent;
  color: var(--dark-brown);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.view-btn:hover {
  background: rgba(38, 166, 154, 0.1);
}

.view-btn.active {
  background: var(--accent-teal);
  color: white;
  box-shadow: 0 2px 8px rgba(38, 166, 154, 0.3);
}

.navigation-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.date-navigation {
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-btn {
  padding: 10px 15px;
  border: 2px solid var(--accent-teal);
  background: white;
  color: var(--accent-teal);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn:hover {
  background: var(--accent-teal);
  color: white;
}

.today-btn {
  background: var(--accent-teal);
  color: white;
}

.today-btn:hover {
  background: #1e8e7e;
}

.current-period {
  min-width: 200px;
  text-align: center;
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--dark-brown);
}

.date-picker input {
  padding: 10px;
  border: 2px solid var(--accent-teal);
  border-radius: 8px;
  font-size: 0.9rem;
  color: var(--dark-brown);
}

.action-controls {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn.primary {
  background: var(--bright-orange);
  color: white;
}

.action-btn.primary:hover {
  background: #e65100;
  transform: translateY(-2px);
}

.action-btn.secondary {
  background: var(--light-beige);
  color: var(--dark-brown);
  border: 2px solid var(--accent-teal);
}

.action-btn.secondary:hover {
  background: var(--accent-teal);
  color: white;
}

/* Calendar Display Area */
.calendar-display-area {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

.calendar-view {
  display: none;
}

.calendar-view.active {
  display: block;
}

/* Month View Styles */
.month-calendar {
  width: 100%;
}

.weekday-headers {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1px;
}

.weekday-header {
  background: var(--accent-teal);
  color: white;
  padding: 15px;
  text-align: center;
  font-weight: bold;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.month-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e0e0e0;
}

.calendar-day {
  background: white;
  min-height: 120px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  border: 2px solid transparent;
}

.calendar-day:hover {
  background: #f8f9fa;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.calendar-day.today {
  background: #e3f2fd;
  border-color: var(--accent-teal);
  box-shadow: 0 0 0 2px rgba(38, 166, 154, 0.3);
}

.calendar-day.other-month {
  background: #f5f5f5;
  color: #999;
}

.calendar-day.has-events {
  background: #f0f9ff;
  border-color: var(--accent-teal);
}

.day-number {
  font-weight: bold;
  font-size: 1.1rem;
  margin-bottom: 8px;
  color: var(--dark-brown);
}

.events-indicator {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-top: auto;
}

.event-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.entry-dot {
  background: var(--bright-orange);
}

.reminder-dot {
  background: var(--accent-teal);
}

/* Week View Styles */
.week-calendar {
  width: 100%;
}

.week-header {
  display: grid;
  grid-template-columns: 80px repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1px;
}

.time-column {
  background: var(--light-beige);
}

.week-day-header {
  background: var(--accent-teal);
  color: white;
  padding: 15px;
  text-align: center;
}

.day-name {
  font-weight: bold;
  font-size: 0.9rem;
  text-transform: uppercase;
}

.day-number {
  font-size: 1.2rem;
  margin-top: 5px;
}

.week-grid {
  display: grid;
  grid-template-columns: 80px repeat(7, 1fr);
  gap: 1px;
  background: #e0e0e0;
  max-height: 600px;
  overflow-y: auto;
}

.time-slot {
  display: contents;
}

.time-label {
  background: var(--light-beige);
  padding: 10px;
  text-align: center;
  font-size: 0.8rem;
  color: var(--dark-brown);
  border-bottom: 1px solid #ddd;
}

.day-slot {
  background: white;
  min-height: 40px;
  padding: 2px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.week-event {
  background: var(--accent-teal);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
  margin: 1px 0;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.week-event.reminder-event {
  background: var(--bright-orange);
}

/* Day View Styles */
.day-calendar {
  width: 100%;
}

.day-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: var(--light-beige);
  border-radius: 12px;
}

.day-header h3 {
  margin: 0;
  color: var(--dark-brown);
  font-size: 1.5rem;
}

.day-content {
  max-height: 500px;
  overflow-y: auto;
}

.day-section {
  margin-bottom: 30px;
}

.day-section h4 {
  color: var(--dark-brown);
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--accent-teal);
  display: flex;
  align-items: center;
  gap: 10px;
}

.day-event {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid var(--accent-teal);
}

.day-event:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.day-event.entry-event {
  border-left-color: var(--bright-orange);
}

.day-event.reminder-event {
  border-left-color: var(--accent-teal);
}

.day-event.completed {
  opacity: 0.7;
  background: #f5f5f5;
}

.event-time {
  font-size: 0.9rem;
  color: var(--accent-teal);
  font-weight: bold;
  margin-bottom: 5px;
}

.event-title {
  font-weight: bold;
  color: var(--dark-brown);
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.event-content {
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.event-tags {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.tag {
  background: var(--light-beige);
  color: var(--dark-brown);
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  border: 1px solid var(--accent-teal);
}

.event-status {
  margin-top: 10px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-badge.completed {
  background: #4caf50;
  color: white;
}

.status-badge.pending {
  background: #ff9800;
  color: white;
}

.no-events {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.no-events i {
  font-size: 4rem;
  margin-bottom: 20px;
  color: var(--accent-teal);
}

.no-events p {
  font-size: 1.2rem;
  margin-bottom: 20px;
}

/* Quick Actions Panel */
.quick-actions-panel {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  text-decoration: none;
  color: var(--dark-brown);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.quick-action-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  text-decoration: none;
  color: var(--dark-brown);
}

.quick-action-btn i {
  font-size: 2rem;
  color: var(--accent-teal);
}

.journal-btn:hover i {
  color: var(--bright-orange);
}

.reminder-btn:hover i {
  color: var(--accent-teal);
}

.list-btn:hover i {
  color: var(--dark-purple);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 0;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 25px 30px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--light-beige);
  border-radius: 16px 16px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: var(--dark-brown);
  font-size: 1.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f0f0f0;
  color: var(--dark-brown);
}

.modal-body {
  padding: 30px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background: #f8f9fa;
  border-radius: 0 0 16px 16px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: var(--accent-teal);
  color: white;
}

.btn-primary:hover {
  background: #1e8e7e;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

/* Quick Add Modal Tabs */
.quick-add-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.tab-btn {
  padding: 12px 24px;
  border: none;
  background: none;
  cursor: pointer;
  font-weight: 500;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-btn.active {
  color: var(--accent-teal);
  border-bottom-color: var(--accent-teal);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--dark-brown);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-teal);
  box-shadow: 0 0 0 3px rgba(38, 166, 154, 0.1);
}

/* Event Details */
.event-details {
  line-height: 1.6;
}

.detail-row {
  margin-bottom: 15px;
}

.detail-row strong {
  color: var(--dark-brown);
  display: inline-block;
  min-width: 100px;
}

.content-preview {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 8px;
  border-left: 4px solid var(--accent-teal);
}

.tags-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

/* Search Modal */
.search-form {
  margin-bottom: 20px;
}

.search-form input[type="text"] {
  width: 100%;
  padding: 15px;
  border: 2px solid var(--accent-teal);
  border-radius: 10px;
  font-size: 1.1rem;
  margin-bottom: 15px;
}

.search-filters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.search-filters label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.search-filters input[type="date"] {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.search-results {
  max-height: 400px;
  overflow-y: auto;
}

.search-result-item {
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-result-item:hover {
  background: #f8f9fa;
  border-color: var(--accent-teal);
}

.day.other-month {
  color: #aaa;
  background: #f8f8f8;
}

/* Calendar actions */
.calendar-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

/* Dark theme overrides */
[data-theme="dark"] .stat-item {
  background: var(--dark-brown);
  color: var(--light-beige);
}

[data-theme="dark"] .month-year-selector select {
  background: var(--dark-brown);
  color: var(--light-beige);
  border-color: var(--accent-teal);
}

[data-theme="dark"] .classic-calendar {
  border: 4px solid #555;
  background: #333;
}

[data-theme="dark"] .calendar-header {
  background: #0077aa;
}

[data-theme="dark"] .binding-hole {
  background: #777;
  border-color: #555;
}

[data-theme="dark"] .calendar-body {
  background: #222;
}

[data-theme="dark"] .weekdays {
  background: #0077aa;
  border-color: #555;
}

[data-theme="dark"] .day {
  background: #444;
  color: #eee;
  border-color: #333;
}

[data-theme="dark"] .day:hover {
  background: #555;
}

[data-theme="dark"] .day.today {
  background: #005577;
  color: #fff;
  border-color: #0099cc;
}

[data-theme="dark"] .day.has-entries {
  background: #2a4a5a;
  border-color: #0099cc;
}

[data-theme="dark"] .day.has-entries:hover {
  background: #3a5a6a;
}

[data-theme="dark"] .day.other-month {
  color: #777;
  background: #3a3a3a;
}

[data-theme="dark"] .entry-count {
  background: #0099cc;
}

[data-theme="dark"] .day.today .entry-count {
  background: #00bbdd;
}

/* Journal by date styles for dark theme */
[data-theme="dark"] .journal-header h2 {
    color: var(--bright-orange);
}

[data-theme="dark"] .entry-card {
    background: var(--dark-brown);
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    border: 1px solid var(--bright-orange);
}

[data-theme="dark"] .entry-title {
    color: var(--bright-orange);
}

[data-theme="dark"] .entry-date {
    color: var(--light-beige);
}

[data-theme="dark"] .entry-preview {
    color: var(--light-beige);
}

[data-theme="dark"] .tag {
    background: var(--dark-purple);
    color: var(--light-beige);
    border: 1px solid var(--bright-orange);
}

[data-theme="dark"] .empty-state {
    background: var(--dark-brown);
    border: 1px solid var(--bright-orange);
}

[data-theme="dark"] .empty-state h3 {
    color: var(--bright-orange);
}

[data-theme="dark"] .empty-state p {
    color: var(--light-beige);
}

[data-theme="dark"] .empty-icon {
    color: var(--bright-orange);
}

/* Responsive Design for Modern Calendar */
@media (max-width: 1200px) {
  .modern-calendar-page {
    padding: 15px;
  }

  .calendar-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .modern-calendar-page {
    padding: 10px;
  }

  .calendar-header-section {
    padding: 20px;
  }

  .calendar-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 10px;
  }

  .calendar-stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .calendar-controls-section {
    flex-direction: column;
    gap: 15px;
    padding: 15px 20px;
  }

  .view-buttons {
    width: 100%;
    justify-content: center;
  }

  .navigation-controls {
    flex-direction: column;
    gap: 10px;
  }

  .date-navigation {
    order: 2;
  }

  .date-picker {
    order: 1;
  }

  .action-controls {
    width: 100%;
    justify-content: center;
  }

  .calendar-display-area {
    padding: 15px;
  }

  .calendar-day {
    min-height: 80px;
    padding: 8px;
  }

  .day-number {
    font-size: 1rem;
  }

  .week-header {
    grid-template-columns: 60px repeat(7, 1fr);
  }

  .week-grid {
    grid-template-columns: 60px repeat(7, 1fr);
  }

  .time-label {
    padding: 5px;
    font-size: 0.7rem;
  }

  .quick-actions-panel {
    flex-direction: column;
    align-items: center;
  }

  .quick-action-btn {
    width: 100%;
    max-width: 200px;
  }

  .modal-content {
    width: 95%;
    margin: 10% auto;
  }

  .modal-header {
    padding: 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-footer {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .calendar-title {
    font-size: 1.5rem;
  }

  .view-btn {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .nav-btn {
    padding: 8px 12px;
  }

  .current-period {
    min-width: 150px;
    font-size: 1rem;
  }

  .calendar-day {
    min-height: 60px;
    padding: 5px;
  }

  .day-number {
    font-size: 0.9rem;
  }

  .event-dot {
    width: 6px;
    height: 6px;
  }
}

/* Dark Theme Support for Modern Calendar */
[data-theme="dark"] .modern-calendar-page {
  background: var(--dark-brown);
}

[data-theme="dark"] .calendar-header-section,
[data-theme="dark"] .calendar-controls-section,
[data-theme="dark"] .calendar-display-area {
  background: var(--dark-purple);
  color: var(--light-beige);
}

[data-theme="dark"] .calendar-title,
[data-theme="dark"] .calendar-subtitle {
  color: var(--light-beige);
}

[data-theme="dark"] .stat-card {
  background: linear-gradient(135deg, var(--accent-teal), #1e8e7e);
}

[data-theme="dark"] .view-btn {
  color: var(--light-beige);
}

[data-theme="dark"] .view-btn:hover {
  background: rgba(38, 166, 154, 0.2);
}

[data-theme="dark"] .view-btn.active {
  background: var(--accent-teal);
  color: white;
}

[data-theme="dark"] .nav-btn {
  background: var(--dark-brown);
  color: var(--accent-teal);
  border-color: var(--accent-teal);
}

[data-theme="dark"] .nav-btn:hover {
  background: var(--accent-teal);
  color: white;
}

[data-theme="dark"] .current-period {
  color: var(--light-beige);
}

[data-theme="dark"] .date-picker input {
  background: var(--dark-brown);
  color: var(--light-beige);
  border-color: var(--accent-teal);
}

[data-theme="dark"] .calendar-day {
  background: var(--dark-brown);
  color: var(--light-beige);
  border-color: #444;
}

[data-theme="dark"] .calendar-day:hover {
  background: #3a3a3a;
}

[data-theme="dark"] .calendar-day.today {
  background: #2a4a5a;
  border-color: var(--accent-teal);
}

[data-theme="dark"] .calendar-day.has-events {
  background: #2a3a4a;
  border-color: var(--accent-teal);
}

[data-theme="dark"] .weekday-header {
  background: var(--accent-teal);
}

[data-theme="dark"] .week-day-header {
  background: var(--accent-teal);
}

[data-theme="dark"] .time-label {
  background: var(--dark-brown);
  color: var(--light-beige);
}

[data-theme="dark"] .day-slot {
  background: var(--dark-brown);
  border-color: #444;
}

[data-theme="dark"] .day-header {
  background: var(--dark-brown);
  color: var(--light-beige);
}

[data-theme="dark"] .day-event {
  background: var(--dark-brown);
  border-color: #444;
  color: var(--light-beige);
}

[data-theme="dark"] .event-time {
  color: var(--accent-teal);
}

[data-theme="dark"] .event-title {
  color: var(--light-beige);
}

[data-theme="dark"] .quick-action-btn {
  background: var(--dark-brown);
  color: var(--light-beige);
}

[data-theme="dark"] .modal-content {
  background: var(--dark-purple);
}

[data-theme="dark"] .modal-header {
  background: var(--dark-brown);
  color: var(--light-beige);
}

[data-theme="dark"] .modal-body {
  color: var(--light-beige);
}

[data-theme="dark"] .modal-footer {
  background: var(--dark-brown);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea,
[data-theme="dark"] .form-group select {
  background: var(--dark-brown);
  color: var(--light-beige);
  border-color: #444;
}

[data-theme="dark"] .form-group label {
  color: var(--light-beige);
}

[data-theme="dark"] .content-preview {
  background: var(--dark-brown);
  color: var(--light-beige);
}

[data-theme="dark"] .search-result-item {
  background: var(--dark-brown);
  color: var(--light-beige);
  border-color: #444;
}

[data-theme="dark"] .search-result-item:hover {
  background: #3a3a3a;
  border-color: var(--accent-teal);
}










