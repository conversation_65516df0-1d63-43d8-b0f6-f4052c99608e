{% extends 'app/base.html' %}
{% load static %}

{% block content %}
<h2 class="page-title">Journal Reminders</h2>

<div class="reminder-header">
    <p><i class="fas fa-bell animated-icon"></i> Set up reminders to maintain your journaling habit</p>
    <div class="reminder-actions">
        <a href="{% url 'reminder_create' %}" class="button pulse-button">
            <i class="fas fa-plus animated-icon"></i> New Reminder
        </a>
    </div>
</div>

<div class="streak-info">
    <div class="streak-card">
        <div class="streak-value">{{ streak.current_streak }}</div>
        <div class="streak-label">Current Streak</div>
    </div>
    <div class="streak-card">
        <div class="streak-value">{{ streak.longest_streak }}</div>
        <div class="streak-label">Longest Streak</div>
    </div>
</div>

<div class="reminder-list">
    {% if reminders %}
        {% for reminder in reminders %}
        <div class="reminder-card {% if reminder.active %}active{% else %}inactive{% endif %}">
            <div class="reminder-content">
                <h3 class="reminder-title">{{ reminder.title }}</h3>
                <div class="reminder-time">
                    <i class="far fa-clock animated-icon"></i> {{ reminder.time }}
                </div>
                <div class="reminder-frequency">
                    <i class="fas fa-calendar-alt animated-icon"></i> 
                    {% if reminder.frequency == 'daily' %}
                        Every day
                    {% elif reminder.frequency == 'weekly' %}
                        Weekly on 
                        {% for day in reminder.days.split %}
                            {{ day|title }}{% if not forloop.last %}, {% endif %}
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
            <div class="reminder-actions">
                <label class="switch">
                    <input type="checkbox" class="reminder-toggle" 
                           data-id="{{ reminder.pk }}" 
                           {% if reminder.active %}checked{% endif %}>
                    <span class="slider round"></span>
                </label>
                <a href="{% url 'reminder_edit' pk=reminder.pk %}" class="button small">
                    <i class="fas fa-edit animated-icon"></i>
                </a>
                <a href="{% url 'reminder_delete' pk=reminder.pk %}" class="button small danger">
                    <i class="fas fa-trash animated-icon"></i>
                </a>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-bell-slash"></i>
            </div>
            <h3>No reminders yet</h3>
            <p>Create reminders to help maintain your journaling habit</p>
            <a href="{% url 'reminder_create' %}" class="button">
                <i class="fas fa-plus animated-icon"></i> Create Your First Reminder
            </a>
        </div>
    {% endif %}
</div>

<style>
    .reminder-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .streak-info {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .streak-card {
        background: var(--card-gradient);
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        flex: 1;
        box-shadow: var(--shadow);
        border: 1px solid var(--border-color);
    }
    
    .streak-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--accent-light);
    }
    
    .reminder-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: var(--card-gradient);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: var(--shadow);
        border: 1px solid var(--border-color);
        transition: var(--transition);
    }
    
    .reminder-card.inactive {
        opacity: 0.6;
    }
    
    .reminder-title {
        margin-bottom: 5px;
    }
    
    .reminder-time, .reminder-frequency {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }
    
    .reminder-actions {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    /* Toggle Switch */
    .switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }
    
    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
    }
    
    .slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
    }
    
    input:checked + .slider {
        background-color: var(--accent-light);
    }
    
    input:focus + .slider {
        box-shadow: 0 0 1px var(--accent-light);
    }
    
    input:checked + .slider:before {
        transform: translateX(26px);
    }
    
    .slider.round {
        border-radius: 34px;
    }
    
    .slider.round:before {
        border-radius: 50%;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        background: var(--card-gradient);
        border-radius: 10px;
        box-shadow: var(--shadow);
        border: 1px solid var(--border-color);
    }
    
    .empty-icon {
        font-size: 3rem;
        color: var(--accent-light);
        margin-bottom: 20px;
    }
    
    .button.small {
        padding: 5px 10px;
        font-size: 0.8rem;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listeners to toggle switches
        const toggles = document.querySelectorAll('.reminder-toggle');
        toggles.forEach(toggle => {
            toggle.addEventListener('change', function() {
                const reminderId = this.dataset.id;
                const active = this.checked;
                
                // Update the UI immediately
                const reminderCard = this.closest('.reminder-card');
                if (active) {
                    reminderCard.classList.add('active');
                    reminderCard.classList.remove('inactive');
                } else {
                    reminderCard.classList.add('inactive');
                    reminderCard.classList.remove('active');
                }
                
                // Send AJAX request to update the reminder
                fetch(`/reminders/${reminderId}/toggle/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({active: active})
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('Error toggling reminder:', data.error);
                        // Revert the UI change if there was an error
                        this.checked = !active;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Revert the UI change if there was an error
                    this.checked = !active;
                });
            });
        });
        
        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}


