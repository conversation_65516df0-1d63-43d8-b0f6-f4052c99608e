{% extends 'app/base.html' %}
{% load static %}

{% block content %}
<div class="confirm-delete-container">
    <div class="confirm-delete-card">
        <h2>Delete Reminder</h2>
        <p>Are you sure you want to delete the reminder "{{ reminder.title }}"?</p>
        <p class="warning">This action cannot be undone.</p>
        
        <div class="reminder-preview">
            <h3>{{ reminder.title }}</h3>
            <div class="reminder-time">
                <i class="far fa-clock animated-icon"></i> {{ reminder.time }}
            </div>
            <div class="reminder-frequency">
                <i class="fas fa-calendar-alt animated-icon"></i> 
                {% if reminder.frequency == 'daily' %}
                    Every day
                {% elif reminder.frequency == 'weekly' %}
                    Weekly on 
                    {% for day in reminder.days.split %}
                        {{ day|title }}{% if not forloop.last %}, {% endif %}
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="form-actions">
                <button type="submit" class="button danger">
                    <i class="fas fa-trash animated-icon"></i> Delete
                </button>
                <a href="{% url 'reminder_list' %}" class="button secondary">
                    <i class="fas fa-times animated-icon"></i> Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<style>
    .confirm-delete-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 70vh;
    }
    
    .confirm-delete-card {
        background: var(--card-gradient);
        border-radius: 12px;
        padding: 2rem;
        width: 100%;
        max-width: 600px;
        box-shadow: var(--shadow);
        border: 1px solid var(--border-color);
        text-align: center;
    }
    
    .confirm-delete-card h2 {
        color: var(--accent-light);
        margin-bottom: 1rem;
    }
    
    .warning {
        color: #e74c3c;
        font-weight: bold;
        margin-bottom: 1.5rem;
    }
    
    .reminder-preview {
        background: rgba(0,0,0,0.2);
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        text-align: left;
    }
    
    .reminder-preview h3 {
        margin-bottom: 0.5rem;
    }
    
    .reminder-time, .reminder-frequency {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }
    
    .form-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }
</style>
{% endblock %}
