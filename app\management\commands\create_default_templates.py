from django.core.management.base import BaseCommand
from app.models import JournalTemplate

class Command(BaseCommand):
    help = 'Creates default journal templates'

    def handle(self, *args, **kwargs):
        # Define default templates
        default_templates = [
            # Original templates
            {
                'name': 'Gratitude Journal',
                'description': 'Focus on the positive aspects of your life by recording things you\'re grateful for.',
                'template_type': 'gratitude',
                'content_structure': """# Three things I'm grateful for today:
1. 
2. 
3. 

# One person I'm thankful for today:

# Something beautiful I noticed today:

# A small win or success today:
""",
                'icon': 'fas fa-heart',
                'is_default': True
            },
            {
                'name': 'Daily Reflection',
                'description': 'Reflect on your day, lessons learned, and goals for tomorrow.',
                'template_type': 'reflection',
                'content_structure': """# How I feel today (1-10):

# Three main activities today:
1. 
2. 
3. 

# What went well today:

# What could have gone better:

# One lesson I learned today:

# My focus for tomorrow:
""",
                'icon': 'fas fa-sun',
                'is_default': True
            },
            
            # New Work-related templates
            {
                'name': 'Work Project Tracker',
                'description': 'Track progress on work projects, tasks, and professional goals.',
                'template_type': 'work',
                'content_structure': """# Project: 

# Today's Key Accomplishments:
1. 
2. 
3. 

# Challenges Encountered:
- 
- 

# Solutions Implemented:
- 
- 

# Action Items for Tomorrow:
- [ ] 
- [ ] 
- [ ] 

# Resources Needed:

# Notes from Meetings:

# Ideas to Explore:
""",
                'icon': 'fas fa-briefcase',
                'is_default': True
            },
            {
                'name': 'Professional Development',
                'description': 'Track your learning, skills development, and career growth.',
                'template_type': 'work',
                'content_structure': """# Skill I'm developing:

# What I learned today:

# Resources used (books, courses, articles):

# How I applied this knowledge:

# Questions that arose:

# People who could help me advance:

# Next steps in my learning journey:

# How this connects to my career goals:
""",
                'icon': 'fas fa-graduation-cap',
                'is_default': True
            },
            {
                'name': 'Meeting Notes',
                'description': 'Structured template for effective meeting documentation.',
                'template_type': 'work',
                'content_structure': """# Meeting Title:

# Date & Time:

# Attendees:
- 
- 

# Agenda Items:
1. 
2. 
3. 

# Key Decisions Made:
- 
- 

# Action Items:
- [ ] Task: ____________ | Owner: ____________ | Due: ____________
- [ ] Task: ____________ | Owner: ____________ | Due: ____________

# Follow-up Required:

# Additional Notes:
""",
                'icon': 'fas fa-users',
                'is_default': True
            },
            
            # Personal templates
            {
                'name': 'Personal Growth Reflection',
                'description': 'Track your personal development journey and inner growth.',
                'template_type': 'personal',
                'content_structure': """# Current mood & energy level:

# What I'm proud of today:

# A challenge I'm working through:

# How I showed up for myself today:

# A boundary I maintained or need to set:

# Self-care activities completed:
- [ ] Physical (exercise, nutrition, sleep)
- [ ] Emotional (processing feelings, therapy)
- [ ] Social (meaningful connections)
- [ ] Spiritual (meditation, nature, purpose)

# Tomorrow, I will prioritize:
""",
                'icon': 'fas fa-seedling',
                'is_default': True
            },
            {
                'name': 'Habit Tracker',
                'description': 'Monitor your daily habits and build consistency.',
                'template_type': 'personal',
                'content_structure': """# Date:

# Habits Tracked:
- [ ] Morning routine completed
- [ ] Water intake goal met
- [ ] Exercise/movement (_____ minutes)
- [ ] Meditation/mindfulness (_____ minutes)
- [ ] Reading (_____ minutes/pages)
- [ ] Healthy meals (_____ /3)
- [ ] Limited screen time
- [ ] Bedtime routine completed

# Habit I'm most proud of today:

# Habit I struggled with:

# What I'll do differently tomorrow:

# Weekly habit goal progress:
""",
                'icon': 'fas fa-check-square',
                'is_default': True
            },
            {
                'name': 'Relationship Journal',
                'description': 'Reflect on your relationships and social connections.',
                'template_type': 'personal',
                'content_structure': """# Key relationship focus:

# Meaningful interactions today:

# How I showed appreciation/love:

# Challenges or conflicts:

# What I learned about myself through others:

# How I can better show up in this relationship:

# Boundaries to maintain:

# Plans for quality time:
""",
                'icon': 'fas fa-hands-helping',
                'is_default': True
            },
            
            # Holiday templates
            {
                'name': 'Vacation Planner',
                'description': 'Plan and document your perfect getaway.',
                'template_type': 'holiday',
                'content_structure': """# Destination:

# Travel Dates:

# Pre-Trip Checklist:
- [ ] Passport/ID
- [ ] Accommodations booked
- [ ] Transportation arranged
- [ ] Activities researched
- [ ] Packing list created
- [ ] Home arrangements (pet/plant care)
- [ ] Work handoff completed

# Must-See Attractions:
1. 
2. 
3. 

# Local Cuisine to Try:
- 
- 

# Budget Planning:
- Accommodation: 
- Transportation: 
- Food: 
- Activities: 
- Shopping: 
- Miscellaneous: 

# Packing Reminders:

# Travel Inspiration (quotes, photos, articles):
""",
                'icon': 'fas fa-suitcase',
                'is_default': True
            },
            {
                'name': 'Holiday Memory Keeper',
                'description': 'Capture and preserve special holiday moments and traditions.',
                'template_type': 'holiday',
                'content_structure': """# Holiday/Celebration:

# Date:

# Location:

# People Present:

# Special Traditions Observed:

# Memorable Moments:

# Foods Enjoyed:

# Gifts Given/Received:

# Photos Taken:

# Feelings and Reflections:

# What I Want to Remember Most:

# Ideas for Next Year:
""",
                'icon': 'fas fa-gifts',
                'is_default': True
            },
            {
                'name': 'Travel Diary',
                'description': 'Document your adventures and discoveries while traveling.',
                'template_type': 'holiday',
                'content_structure': """# Day # of Trip:

# Location:

# Weather & Conditions:

# Today's Itinerary:
- Morning: 
- Afternoon: 
- Evening: 

# Highlight of the Day:

# New Foods Tried:

# Cultural Observations:

# People Met:

# Unexpected Discoveries:

# Challenges Navigated:

# Photos Taken:

# Local Phrases Learned:

# Tomorrow's Plan:
""",
                'icon': 'fas fa-globe-americas',
                'is_default': True
            },
            
            # Other templates
            {
                'name': 'Creative Project Planner',
                'description': 'Organize your creative projects from inspiration to completion.',
                'template_type': 'other',
                'content_structure': """# Project Name:

# Project Type:

# Inspiration & Vision:

# Goals for this Project:

# Materials/Resources Needed:

# Project Milestones:
1. 
2. 
3. 

# Current Status:

# Challenges to Overcome:

# Next Steps:

# Ideas to Explore:

# Reference Materials:

# Feedback Received:

# Target Completion Date:
""",
                'icon': 'fas fa-paint-brush',
                'is_default': True
            },
            {
                'name': 'Book Review',
                'description': 'Analyze and reflect on books you\'ve read.',
                'template_type': 'other',
                'content_structure': """# Book Title:

# Author:

# Genre:

# Date Started/Finished:

# Rating (1-5 stars):

# Key Themes:

# Favorite Quotes:
> 
> 
> 

# Main Characters:

# Plot Summary:

# What I Liked:

# What I Disliked:

# Personal Takeaways:

# Would I Recommend This Book? Why/Why Not?:

# Similar Books to Explore:
""",
                'icon': 'fas fa-book-open',
                'is_default': True
            },
            {
                'name': 'Health & Wellness Tracker',
                'description': 'Monitor your physical and mental wellbeing.',
                'template_type': 'other',
                'content_structure': """# Date:

# Overall Wellbeing (1-10):

# Physical Health:
- Energy Level (1-10): 
- Sleep Quality (1-10): 
- Hours of Sleep: 
- Exercise Completed: 
- Nutrition Highlights: 
- Water Intake: 
- Physical Symptoms: 

# Mental Health:
- Mood (1-10): 
- Stress Level (1-10): 
- Anxiety Level (1-10): 
- Mental Clarity (1-10): 

# Self-Care Activities:

# Medications/Supplements Taken:

# Health Goals Progress:

# Notes for Healthcare Provider:

# Adjustments for Tomorrow:
""",
                'icon': 'fas fa-heartbeat',
                'is_default': True
            }
        ]

        # Create templates if they don't exist
        templates_created = 0
        for template_data in default_templates:
            template, created = JournalTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            if created:
                templates_created += 1
                self.stdout.write(self.style.SUCCESS(f'Created template: {template.name}'))
            else:
                self.stdout.write(f'Template already exists: {template.name}')

        self.stdout.write(self.style.SUCCESS(f'Successfully created {templates_created} default templates'))
