{% extends 'app/base.html' %}

{% block content %}
<h2 class="page-title">My Journal Entries</h2>

{% if user.is_authenticated %}
<div class="search-form">
    <form method="get" action="{% url 'journal_search' %}">
        <input type="text" name="q" placeholder="Search entries..." value="{{ query|default:'' }}">
        <button type="submit"><i class="fas fa-search animated-icon"></i> Search</button>
    </form>
</div>
{% endif %}

<div class="journal-header">
    <p><i class="fas fa-book-open animated-icon"></i> Your personal space for thoughts and memories</p>
    <div class="journal-actions">
        <a href="{% url 'calendar_view' %}" class="button secondary">
            <i class="fas fa-calendar-alt animated-icon"></i> Calendar View
        </a>
        {% if user.is_authenticated %}
        <a href="{% url 'journal_new' %}" class="button pulse-button">
            <i class="fas fa-plus animated-icon"></i> New Entry
        </a>
        {% endif %}
    </div>
</div>

<style>
.journal-actions {
    display: flex;
    gap: 10px;
}

.journal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
</style>

<div class="entry-list">
    {% for entry in entries %}
    <div class="entry-card">
        {% if entry.image %}
        <div class="entry-image" onclick="openModal('{{ entry.image.url }}')">
            <img src="{{ entry.image.url }}" alt="{{ entry.title }}">
        </div>
        {% endif %}
        <div class="entry-content">
            <h3 class="entry-title">{{ entry.title }}</h3>
            <div class="entry-date">
                <i class="far fa-calendar-alt animated-icon"></i> {{ entry.created_at|date:"F j, Y" }}
            </div>
            <p>{{ entry.content|truncatewords:20 }}</p>
            <div class="entry-actions">
                <a href="{% url 'journal_detail' pk=entry.pk %}" class="button"><i class="fas fa-book-reader animated-icon"></i> Read More</a>
            </div>
            {% if entry.tags %}
            <div class="entry-tags">
                {% for tag in entry.tags.split %}
                <span class="tag">{{ tag|stringformat:"s" }}</span>
                {% endfor %}
                {% if entry.tags.split|length > 3 %}
                <span class="tag more">+{{ entry.tags.split|length|add:"-3" }}</span>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
    {% empty %}
    <div class="empty-state">
        {% if user.is_authenticated %}
        <p>You haven't created any journal entries yet.</p>
        <a href="{% url 'journal_new' %}" class="button pulse-button">
            <i class="fas fa-plus animated-icon"></i> Create Your First Entry
        </a>
        {% else %}
        <p>Please log in to view and create journal entries.</p>
        <a href="{% url 'login' %}" class="button pulse-button">
            <i class="fas fa-sign-in-alt animated-icon"></i> Log In
        </a>
        {% endif %}
    </div>
    {% endfor %}
</div>
{% endblock %}



















