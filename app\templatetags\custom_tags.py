from django import template
from django.templatetags.static import static as django_static

register = template.Library()

@register.simple_tag
def custom_static(path):
    """
    A wrapper around the static tag to ensure it works properly
    """
    return django_static(path)

@register.filter
def get_item(dictionary, key):
    """
    Get an item from a dictionary using a key
    """
    return dictionary.get(key)
