from django.shortcuts import render, redirect, get_object_or_404
from django.utils import timezone
from django.views.decorators.cache import never_cache
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse
from .models import JournalEntry
from .forms import JournalEntryForm
from django.db.models import Q
from datetime import datetime
from bson.objectid import ObjectId
from .utils.mongodb_utils import get_db
import json
from django.http import JsonResponse

# MongoDB collections
def get_collections():
    db = get_db()
    return {
        'users': db['users'],
        'journal_entries': db['journal_entries'],
        'journal_streaks': db['journal_streaks'],
        'journal_reminders': db['journal_reminders'],
    }

@never_cache
def home(request):
    # Add a timestamp to force cache refresh
    request.timestamp = timezone.now().timestamp()
    return render(request, 'app/home.html')

def signup_view(request):
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            # Create Django user for authentication
            user = form.save()

            # Store additional user data in MongoDB
            collections = get_collections()
            collections['users'].insert_one({
                'django_id': user.id,
                'username': user.username,
                'email': user.email,
                'date_joined': timezone.now(),
                'last_login': timezone.now(),
                'is_active': True
            })

            # Initialize streak for the user
            collections['journal_streaks'].insert_one({
                'user_id': user.id,
                'current_streak': 0,
                'longest_streak': 0,
                'last_entry_date': None
            })

            login(request, user)
            messages.success(request, "Account created successfully!")
            return redirect('home')
    else:
        form = UserCreationForm()
    return render(request, 'app/signup.html', {'form': form})

def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)

            # Update last login in MongoDB
            collections = get_collections()
            collections['users'].update_one(
                {'django_id': user.id},
                {'$set': {'last_login': timezone.now()}}
            )

            messages.success(request, "You have been logged in!")
            next_url = request.GET.get('next')
            if next_url:
                return redirect(next_url)
            return redirect('home')
    else:
        form = AuthenticationForm()
    return render(request, 'app/login.html', {'form': form})

@login_required
def logout_view(request):
    logout(request)
    messages.success(request, "You have been logged out!")
    return redirect('home')

@login_required
def profile_view(request):
    collections = get_collections()

    # Get user data
    user_data = collections['users'].find_one({'django_id': request.user.id})

    # Get streak data
    streak_data = collections['journal_streaks'].find_one({'user_id': request.user.id})

    # Get total entries count
    total_entries = collections['journal_entries'].count_documents({'user_id': request.user.id})

    # Get entries by month
    from datetime import datetime
    current_year = datetime.now().year
    entries_by_month = []

    for month in range(1, 13):
        start_date = datetime(current_year, month, 1)
        if month == 12:
            end_date = datetime(current_year + 1, 1, 1)
        else:
            end_date = datetime(current_year, month + 1, 1)

        count = collections['journal_entries'].count_documents({
            'user_id': request.user.id,
            'created_at': {
                '$gte': start_date,
                '$lt': end_date
            }
        })

        entries_by_month.append({
            'month': start_date.strftime('%B'),
            'count': count
        })

    return render(request, 'app/profile.html', {
        'user_data': user_data,
        'streak_data': streak_data,
        'total_entries': total_entries,
        'entries_by_month': entries_by_month
    })

# Journal views
@login_required
def journal_list(request):
    collections = get_collections()
    entries = list(collections['journal_entries'].find(
        {'user_id': request.user.id}
    ).sort('created_at', -1))

    # Convert ObjectId to string for template rendering
    for entry in entries:
        entry['_id'] = str(entry['_id'])

    return render(request, 'app/journal_list.html', {'entries': entries})

@login_required
def journal_detail(request, pk):
    collections = get_collections()
    entry = collections['journal_entries'].find_one({
        '_id': ObjectId(pk),
        'user_id': request.user.id
    })

    if not entry:
        messages.error(request, "Journal entry not found.")
        return redirect('journal_list')

    # Convert ObjectId to string for template rendering
    entry['_id'] = str(entry['_id'])

    return render(request, 'app/journal_detail.html', {'entry': entry})

@login_required
def journal_new(request):
    if request.method == "POST":
        form = JournalEntryForm(request.POST, request.FILES)
        if form.is_valid():
            # Process the form data
            entry_data = {
                'user_id': request.user.id,
                'title': form.cleaned_data['title'],
                'content': form.cleaned_data['content'],
                'created_at': timezone.now(),
                'updated_at': timezone.now(),
                'category': form.cleaned_data['category'],
                'tags': form.cleaned_data['tags'],
            }

            # Handle image upload
            if request.FILES.get('image'):
                # Save the image to media storage
                image = request.FILES['image']
                image_path = f'journal_images/{request.user.id}/{timezone.now().strftime("%Y%m%d%H%M%S")}_{image.name}'

                # Save the image to the file system
                from django.core.files.storage import default_storage
                with default_storage.open(image_path, 'wb+') as destination:
                    for chunk in image.chunks():
                        destination.write(chunk)

                entry_data['image'] = image_path

            # Insert into MongoDB
            collections = get_collections()
            result = collections['journal_entries'].insert_one(entry_data)

            # Update user streak
            update_streak(request.user.id)

            messages.success(request, "Journal entry created successfully!")
            return redirect('journal_detail', pk=str(result.inserted_id))
    else:
        form = JournalEntryForm()
    return render(request, 'app/journal_form.html', {'form': form})

@login_required
def journal_edit(request, pk):
    collections = get_collections()
    entry = collections['journal_entries'].find_one({
        '_id': ObjectId(pk),
        'user_id': request.user.id
    })

    if not entry:
        messages.error(request, "Journal entry not found.")
        return redirect('journal_list')

    if request.method == "POST":
        form = JournalEntryForm(request.POST, request.FILES)
        if form.is_valid():
            # Process the form data
            update_data = {
                'title': form.cleaned_data['title'],
                'content': form.cleaned_data['content'],
                'updated_at': timezone.now(),
                'category': form.cleaned_data['category'],
                'tags': form.cleaned_data['tags'],
            }

            # Handle image upload
            if request.FILES.get('image'):
                # Save the image to media storage
                image = request.FILES['image']
                image_path = f'journal_images/{request.user.id}/{timezone.now().strftime("%Y%m%d%H%M%S")}_{image.name}'

                # Save the image to the file system
                from django.core.files.storage import default_storage
                with default_storage.open(image_path, 'wb+') as destination:
                    for chunk in image.chunks():
                        destination.write(chunk)

                update_data['image'] = image_path

            # Update in MongoDB
            collections['journal_entries'].update_one(
                {'_id': ObjectId(pk)},
                {'$set': update_data}
            )

            messages.success(request, "Journal entry updated successfully!")
            return redirect('journal_detail', pk=pk)
    else:
        # Create a form instance with the entry data
        initial_data = {
            'title': entry.get('title', ''),
            'content': entry.get('content', ''),
            'category': entry.get('category', ''),
            'tags': entry.get('tags', ''),
        }
        form = JournalEntryForm(initial=initial_data)

    return render(request, 'app/journal_form.html', {
        'form': form,
        'entry': entry,
        'is_edit': True
    })

@login_required
def journal_delete(request, pk):
    collections = get_collections()
    entry = collections['journal_entries'].find_one({
        '_id': ObjectId(pk),
        'user_id': request.user.id
    })

    if not entry:
        messages.error(request, "Journal entry not found.")
        return redirect('journal_list')

    if request.method == "POST":
        # Delete the entry from MongoDB
        collections['journal_entries'].delete_one({'_id': ObjectId(pk)})

        # Delete the image if it exists
        if entry.get('image'):
            from django.core.files.storage import default_storage
            if default_storage.exists(entry['image']):
                default_storage.delete(entry['image'])

        messages.success(request, "Journal entry deleted successfully!")
        return redirect('journal_list')

    return render(request, 'app/journal_confirm_delete.html', {'entry': entry})

@login_required
def journal_search(request):
    query = request.GET.get('q', '')
    collections = get_collections()

    if query:
        # Search in MongoDB using a text search or regex
        entries = list(collections['journal_entries'].find({
            'user_id': request.user.id,
            '$or': [
                {'title': {'$regex': query, '$options': 'i'}},
                {'content': {'$regex': query, '$options': 'i'}},
                {'tags': {'$regex': query, '$options': 'i'}}
            ]
        }).sort('created_at', -1))
    else:
        # If no query, return all entries
        entries = list(collections['journal_entries'].find(
            {'user_id': request.user.id}
        ).sort('created_at', -1))

    # Convert ObjectId to string for template rendering
    for entry in entries:
        entry['_id'] = str(entry['_id'])

    return render(request, 'app/journal_list.html', {
        'entries': entries,
        'query': query
    })

@login_required
def journal_by_date(request, date_str):
    try:
        # Parse the date string
        from datetime import datetime
        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

        # Get entries for this date
        collections = get_collections()

        # Find entries created on this date
        from datetime import timedelta
        start_date = datetime.combine(date_obj, datetime.min.time())
        end_date = datetime.combine(date_obj, datetime.max.time())

        entries = list(collections['journal_entries'].find({
            'user_id': request.user.id,
            'created_at': {
                '$gte': start_date,
                '$lte': end_date
            }
        }).sort('created_at', -1))

        # Convert ObjectId to string for template rendering
        for entry in entries:
            entry['_id'] = str(entry['_id'])

        # Format the date for display
        formatted_date = date_obj.strftime('%B %d, %Y')

        return render(request, 'app/journal_by_date.html', {
            'entries': entries,
            'formatted_date': formatted_date,
            'date_str': date_str
        })
    except ValueError:
        messages.error(request, "Invalid date format.")
        return redirect('journal_list')

# Reminder views
@login_required
def reminder_list(request):
    collections = get_collections()

    # Get all reminders for the user
    reminders = list(collections['journal_reminders'].find({
        'user_id': request.user.id
    }).sort('time', 1))  # Sort by time

    # Convert ObjectId to string for template rendering
    for reminder in reminders:
        reminder['pk'] = str(reminder['_id'])

    # Get streak data for display
    streak_doc = collections['journal_streaks'].find_one({'user_id': request.user.id})
    streak = {
        'current_streak': streak_doc.get('current_streak', 0) if streak_doc else 0,
        'longest_streak': streak_doc.get('longest_streak', 0) if streak_doc else 0
    }

    return render(request, 'app/reminder_list.html', {
        'reminders': reminders,
        'streak': streak
    })

@login_required
def reminder_create(request):
    if request.method == 'POST':
        # Process form data
        title = request.POST.get('title')
        time = request.POST.get('time')
        frequency = request.POST.get('frequency')
        days = request.POST.getlist('days')

        if not title or not time:
            messages.error(request, "Title and time are required.")
            return redirect('reminder_create')

        # Convert time string to datetime.time object
        from datetime import datetime
        time_obj = datetime.strptime(time, '%H:%M').time()

        # Create reminder in MongoDB
        collections = get_collections()
        collections['journal_reminders'].insert_one({
            'user_id': request.user.id,
            'title': title,
            'time': time_obj.strftime('%H:%M'),
            'frequency': frequency,
            'days': ' '.join(days) if days else '',
            'active': True,
            'created_at': timezone.now()
        })

        messages.success(request, "Reminder created successfully!")
        return redirect('reminder_list')

    return render(request, 'app/reminder_form.html', {'is_edit': False})

@login_required
def reminder_edit(request, pk):
    collections = get_collections()

    # Get the reminder
    reminder = collections['journal_reminders'].find_one({
        '_id': ObjectId(pk),
        'user_id': request.user.id
    })

    if not reminder:
        messages.error(request, "Reminder not found.")
        return redirect('reminder_list')

    if request.method == 'POST':
        # Process form data
        title = request.POST.get('title')
        time = request.POST.get('time')
        frequency = request.POST.get('frequency')
        days = request.POST.getlist('days')

        if not title or not time:
            messages.error(request, "Title and time are required.")
            return redirect('reminder_edit', pk=pk)

        # Convert time string to datetime.time object
        from datetime import datetime
        time_obj = datetime.strptime(time, '%H:%M').time()

        # Update reminder in MongoDB
        collections['journal_reminders'].update_one(
            {'_id': ObjectId(pk)},
            {'$set': {
                'title': title,
                'time': time_obj.strftime('%H:%M'),
                'frequency': frequency,
                'days': ' '.join(days) if days else '',
                'updated_at': timezone.now()
            }}
        )

        messages.success(request, "Reminder updated successfully!")
        return redirect('reminder_list')

    # Convert days string to list for form
    if 'days' in reminder and reminder['days']:
        reminder['days_list'] = reminder['days'].split()
    else:
        reminder['days_list'] = []

    return render(request, 'app/reminder_form.html', {
        'reminder': reminder,
        'is_edit': True
    })

@login_required
def reminder_delete(request, pk):
    collections = get_collections()

    # Get the reminder
    reminder = collections['journal_reminders'].find_one({
        '_id': ObjectId(pk),
        'user_id': request.user.id
    })

    if not reminder:
        messages.error(request, "Reminder not found.")
        return redirect('reminder_list')

    if request.method == 'POST':
        # Delete the reminder
        collections['journal_reminders'].delete_one({'_id': ObjectId(pk)})

        messages.success(request, "Reminder deleted successfully!")
        return redirect('reminder_list')

    return render(request, 'app/reminder_confirm_delete.html', {'reminder': reminder})

@login_required
def reminder_toggle(request, pk):
    """Toggle a reminder's active status via AJAX"""
    if request.method == 'POST':
        import json
        data = json.loads(request.body)
        active = data.get('active', False)

        collections = get_collections()

        # Update the reminder
        result = collections['journal_reminders'].update_one(
            {'_id': ObjectId(pk), 'user_id': request.user.id},
            {'$set': {'active': active}}
        )

        if result.modified_count > 0:
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'error': 'Reminder not found'})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})

def update_streak(user_id):
    """Update the user's journal streak"""
    collections = get_collections()
    today = timezone.now().date()

    # Get current streak info
    streak_doc = collections['journal_streaks'].find_one({'user_id': user_id})

    if not streak_doc:
        # Initialize streak if it doesn't exist
        collections['journal_streaks'].insert_one({
            'user_id': user_id,
            'current_streak': 1,
            'longest_streak': 1,
            'last_entry_date': today
        })
        return

    last_entry_date = streak_doc.get('last_entry_date')
    current_streak = streak_doc.get('current_streak', 0)
    longest_streak = streak_doc.get('longest_streak', 0)

    # Convert string date to date object if needed
    if isinstance(last_entry_date, str):
        from datetime import datetime
        last_entry_date = datetime.strptime(last_entry_date, '%Y-%m-%d').date()

    if last_entry_date is None:
        # First entry
        new_streak = 1
    elif last_entry_date == today:
        # Already journaled today, no change
        new_streak = current_streak
    elif (today - last_entry_date).days == 1:
        # Consecutive day
        new_streak = current_streak + 1
    else:
        # Streak broken
        new_streak = 1

    # Update longest streak if needed
    if new_streak > longest_streak:
        longest_streak = new_streak

    # Update in MongoDB
    collections['journal_streaks'].update_one(
        {'user_id': user_id},
        {'$set': {
            'current_streak': new_streak,
            'longest_streak': longest_streak,
            'last_entry_date': today
        }}
    )

@login_required
def calendar_view(request):
    from datetime import datetime, timedelta
    import calendar as cal
    import json

    collections = get_collections()

    # Get current date or requested month/year
    today = datetime.now().date()
    current_month = int(request.GET.get('month', today.month))
    current_year = int(request.GET.get('year', today.year))

    # Validate month and year
    if current_month < 1 or current_month > 12:
        current_month = today.month
    if current_year < 1900 or current_year > 2100:
        current_year = today.year

    # Get all journal entries for the user
    entries = list(collections['journal_entries'].find({'user_id': request.user.id}))

    # Organize entries by date with more detailed information
    entries_by_date = {}
    total_entries = 0

    for entry in entries:
        date_str = entry['created_at'].strftime('%Y-%m-%d')
        if date_str not in entries_by_date:
            entries_by_date[date_str] = {
                'count': 0,
                'entries': []
            }
        entries_by_date[date_str]['count'] += 1
        entries_by_date[date_str]['entries'].append({
            'id': str(entry['_id']),
            'title': entry['title'][:50] + ('...' if len(entry['title']) > 50 else ''),
            'created_at': entry['created_at'].strftime('%H:%M')
        })
        total_entries += 1

    # Get calendar information for the current month
    month_calendar = cal.monthcalendar(current_year, current_month)
    month_name = cal.month_name[current_month]

    # Calculate previous and next month/year
    prev_month = current_month - 1
    prev_year = current_year
    if prev_month < 1:
        prev_month = 12
        prev_year -= 1

    next_month = current_month + 1
    next_year = current_year
    if next_month > 12:
        next_month = 1
        next_year += 1

    # Convert to JSON for JavaScript
    calendar_data = json.dumps(entries_by_date)

    context = {
        'calendar_data': calendar_data,
        'current_month': current_month,
        'current_year': current_year,
        'month_name': month_name,
        'today': today,
        'prev_month': prev_month,
        'prev_year': prev_year,
        'next_month': next_month,
        'next_year': next_year,
        'total_entries': total_entries,
        'entries_this_month': len([d for d in entries_by_date.keys()
                                 if d.startswith(f'{current_year}-{current_month:02d}')])
    }

    return render(request, 'app/calendar.html', context)
