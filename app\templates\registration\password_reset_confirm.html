{% extends 'app/base.html' %}

{% block content %}
<div class="auth-container">
    <h2>Set New Password</h2>
    
    {% if validlink %}
    <form method="post">
        {% csrf_token %}
        <div class="form-group">
            <label for="id_new_password1">New password:</label>
            {{ form.new_password1 }}
            {% if form.new_password1.errors %}
            <div class="error-message">{{ form.new_password1.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_new_password2">Confirm password:</label>
            {{ form.new_password2 }}
            {% if form.new_password2.errors %}
            <div class="error-message">{{ form.new_password2.errors }}</div>
            {% endif %}
        </div>
        <button type="submit">Change Password</button>
    </form>
    {% else %}
    <p>The password reset link was invalid, possibly because it has already been used. Please request a new password reset.</p>
    <p class="auth-link"><a href="{% url 'password_reset' %}">Request new reset link</a></p>
    {% endif %}
</div>
{% endblock %}