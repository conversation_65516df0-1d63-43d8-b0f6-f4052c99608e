{% extends 'app/base.html' %}
{% load static %}

{% block content %}
<div class="form-container">
    <div class="form-card">
        <h2>{% if is_edit %}Edit{% else %}Create{% endif %} Reminder</h2>
        
        <form method="post">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="id_title">Title</label>
                <input type="text" id="id_title" name="title" required 
                       value="{% if reminder %}{{ reminder.title }}{% endif %}"
                       placeholder="e.g., Evening Journal Reminder">
            </div>
            
            <div class="form-group">
                <label for="id_time">Time</label>
                <input type="time" id="id_time" name="time" required
                       value="{% if reminder %}{{ reminder.time }}{% endif %}">
            </div>
            
            <div class="form-group">
                <label for="id_frequency">Frequency</label>
                <select id="id_frequency" name="frequency">
                    <option value="daily" {% if reminder.frequency == 'daily' %}selected{% endif %}>Daily</option>
                    <option value="weekly" {% if reminder.frequency == 'weekly' %}selected{% endif %}>Weekly</option>
                </select>
            </div>
            
            <div class="form-group" id="daysGroup">
                <label>Days of Week</label>
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="days" value="monday"
                               {% if reminder and 'monday' in reminder.days_list %}checked{% endif %}>
                        Monday
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="days" value="tuesday"
                               {% if reminder and 'tuesday' in reminder.days_list %}checked{% endif %}>
                        Tuesday
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="days" value="wednesday"
                               {% if reminder and 'wednesday' in reminder.days_list %}checked{% endif %}>
                        Wednesday
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="days" value="thursday"
                               {% if reminder and 'thursday' in reminder.days_list %}checked{% endif %}>
                        Thursday
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="days" value="friday"
                               {% if reminder and 'friday' in reminder.days_list %}checked{% endif %}>
                        Friday
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="days" value="saturday"
                               {% if reminder and 'saturday' in reminder.days_list %}checked{% endif %}>
                        Saturday
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="days" value="sunday"
                               {% if reminder and 'sunday' in reminder.days_list %}checked{% endif %}>
                        Sunday
                    </label>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="button primary-btn">
                    <i class="fas fa-save animated-icon"></i> 
                    {% if is_edit %}Update{% else %}Create{% endif %} Reminder
                </button>
                <a href="{% url 'reminder_list' %}" class="button secondary-btn">
                    <i class="fas fa-times animated-icon"></i> Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const frequencySelect = document.getElementById('id_frequency');
        const daysGroup = document.getElementById('daysGroup');
        
        // Function to toggle days visibility based on frequency
        function toggleDaysVisibility() {
            if (frequencySelect.value === 'daily') {
                daysGroup.style.display = 'none';
            } else {
                daysGroup.style.display = 'block';
            }
        }
        
        // Initial state
        toggleDaysVisibility();
        
        // Listen for changes
        frequencySelect.addEventListener('change', toggleDaysVisibility);
    });
</script>
{% endblock %}
