{% extends 'app/base.html' %}

{% block content %}
<div class="form-container">
    <h2>{% if form.instance.pk %}Edit Journal Entry{% else %}New Journal Entry{% endif %}</h2>
    
    <form method="POST" enctype="multipart/form-data" id="journalForm">
        {% csrf_token %}
        
        <div class="form-group">
            <label for="{{ form.title.id_for_label }}">Title</label>
            {{ form.title }}
            {% if form.title.errors %}
                <div class="form-error">{{ form.title.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.entry_date.id_for_label }}">Entry Date</label>
            {{ form.entry_date }}
            <div class="form-help">{{ form.entry_date.help_text }}</div>
            {% if form.entry_date.errors %}
                <div class="form-error">{{ form.entry_date.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.content.id_for_label }}">Content</label>
            {{ form.content }}
            {% if form.content.errors %}
                <div class="form-error">{{ form.content.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_category">Category:</label>
            {{ form.category }}
            {% if form.category.errors %}
                <div class="error-message">{{ form.category.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_tags">Tags:</label>
            {{ form.tags }}
            {% if form.tags.errors %}
                <div class="error-message">{{ form.tags.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_image">Image:</label>
            {{ form.image }}
            {% if form.image.errors %}
                <div class="error-message">{{ form.image.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_audio">Audio:</label>
            {{ form.audio }}
            {% if form.audio.errors %}
                <div class="error-message">{{ form.audio.errors }}</div>
            {% endif %}
        </div>
        <div class="form-actions">
            <button type="submit" class="button primary-btn">
                <i class="fas fa-save animated-icon"></i> Save Entry
            </button>
            <a href="{% url 'journal_list' %}" class="button secondary" id="cancelBtn">
                <i class="fas fa-times-circle animated-icon"></i> Cancel
            </a>
        </div>
    </form>
</div>

<!-- Confirmation Dialog -->
<div class="confirm-dialog" id="cancelConfirmDialog">
    <div class="confirm-dialog-content">
        <h3><i class="fas fa-exclamation-triangle" style="color: #ff4b2b; margin-right: 10px;"></i> Discard Changes?</h3>
        <p>You have unsaved changes that will be lost if you leave this page. Are you sure you want to discard your entry?</p>
        <div class="confirm-dialog-actions">
            <a href="{% url 'journal_list' %}" class="button danger">
                <i class="fas fa-trash-alt animated-icon"></i> Discard
            </a>
            <button type="button" class="button secondary" id="continueEditingBtn">
                <i class="fas fa-edit animated-icon"></i> Keep Editing
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Voice input code (unchanged)
        const voiceInputBtn = document.getElementById('voiceInputBtn');
        const voiceStatus = document.getElementById('voiceStatus');
        
        // Form change tracking and cancel confirmation
        const form = document.getElementById('journalForm');
        const cancelBtn = document.getElementById('cancelBtn');
        const cancelDialog = document.getElementById('cancelConfirmDialog');
        const continueEditingBtn = document.getElementById('continueEditingBtn');
        
        let formChanged = false;
        
        // Track form changes
        const formInputs = form.querySelectorAll('input, textarea, select');
        formInputs.forEach(input => {
            input.addEventListener('change', () => {
                formChanged = true;
            });
            input.addEventListener('keyup', () => {
                formChanged = true;
            });
        });
        
        // Cancel button click handler
        cancelBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (formChanged) {
                // Show confirmation dialog
                cancelDialog.classList.add('active');
            } else {
                // No changes, redirect immediately
                window.location.href = "{% url 'journal_list' %}";
            }
        });
        
        // Continue editing button
        if (continueEditingBtn) {
            continueEditingBtn.addEventListener('click', function() {
                cancelDialog.classList.remove('active');
            });
        }
        
        // Close dialog when clicking outside
        if (cancelDialog) {
            cancelDialog.addEventListener('click', function(e) {
                if (e.target === this) {
                    cancelDialog.classList.remove('active');
                }
            });
        }
    });
</script>
{% endblock %}








