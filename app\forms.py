from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm

class JournalEntryForm(forms.Form):
    title = forms.CharField(max_length=200, required=True, widget=forms.TextInput(attrs={
        'placeholder': 'Enter a title for your journal entry'
    }))
    content = forms.CharField(widget=forms.Textarea(attrs={
        'rows': 10,
        'placeholder': 'Write your thoughts here...'
    }))
    image = forms.ImageField(required=False)
    category = forms.CharField(max_length=50, required=False, widget=forms.TextInput(attrs={
        'placeholder': 'e.g., Personal, Work, Travel'
    }))
    tags = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={
        'placeholder': 'Enter tags separated by spaces'
    }))




