# Generated by Django 3.1.12 on 2025-05-13 19:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='journalreminder',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='journalreminder',
            name='days',
            field=models.CharField(blank=True, help_text='Comma-separated days for weekly reminders (e.g., Mon,Wed,Fri)', max_length=100),
        ),
        migrations.AlterField(
            model_name='journalreminder',
            name='frequency',
            field=models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('custom', 'Custom')], default='daily', max_length=10),
        ),
        migrations.AlterField(
            model_name='journalreminder',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='journaltemplate',
            name='template_type',
            field=models.CharField(choices=[('gratitude', 'Gratitude Journal'), ('reflection', 'Daily Reflection'), ('dream', 'Dream Log'), ('goal', 'Goal Setting'), ('travel', 'Travel Journal'), ('food', 'Food Journal'), ('work', 'Work & Professional'), ('personal', 'Personal Growth'), ('holiday', 'Holidays & Travel'), ('other', 'Other Templates'), ('custom', 'Custom Template')], max_length=20),
        ),
    ]
