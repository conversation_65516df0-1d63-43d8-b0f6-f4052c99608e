from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

class JournalEntry(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='journal_entries')
    title = models.CharField(max_length=200)
    content = models.TextField()
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    image = models.ImageField(upload_to='journal_images/', blank=True, null=True)
    category = models.CharField(max_length=100, blank=True)
    tags = models.CharField(max_length=200, blank=True, help_text="Comma-separated tags")
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = "Journal Entries"

class JournalTemplate(models.Model):
    TEMPLATE_TYPES = [
        ('gratitude', 'Gratitude Journal'),
        ('reflection', 'Daily Reflection'),
        ('dream', 'Dream Log'),
        ('goal', 'Goal Setting'),
        ('travel', 'Travel Journal'),
        ('food', 'Food Journal'),
        ('work', 'Work & Professional'),
        ('personal', 'Personal Growth'),
        ('holiday', 'Holidays & Travel'),
        ('other', 'Other Templates'),
        ('custom', 'Custom Template'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    content_structure = models.TextField(help_text="Template structure with placeholders")
    icon = models.CharField(max_length=50, default="fas fa-book")
    is_default = models.BooleanField(default=False)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    def __str__(self):
        return self.name

class JournalStreak(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='journal_streak')
    current_streak = models.IntegerField(default=0)
    longest_streak = models.IntegerField(default=0)
    last_entry_date = models.DateField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.user.username}'s streak: {self.current_streak} days"

class JournalReminder(models.Model):
    FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('custom', 'Custom'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    title = models.CharField(max_length=100)
    frequency = models.CharField(max_length=10, choices=FREQUENCY_CHOICES, default='daily')
    time = models.TimeField()
    days = models.CharField(max_length=100, blank=True, help_text="Comma-separated days for weekly reminders (e.g., Mon,Wed,Fri)")
    active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.title} - {self.get_frequency_display()} at {self.time.strftime('%I:%M %p')}"
