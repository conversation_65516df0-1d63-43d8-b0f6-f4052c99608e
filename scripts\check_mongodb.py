"""
<PERSON><PERSON><PERSON> to check MongoDB connection
Run this script to verify that your MongoDB connection is working properly
"""

import os
import sys
import django
from pymongo import MongoClient
import time

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.conf import settings

def check_mongodb_connection():
    """Check MongoDB connection and print status"""
    print("Checking MongoDB connection...")
    
    try:
        # Get connection parameters from settings
        host = settings.DATABASES['default']['CLIENT']['host']
        port = settings.DATABASES['default']['CLIENT']['port']
        username = settings.DATABASES['default']['CLIENT'].get('username', '')
        password = settings.DATABASES['default']['CLIENT'].get('password', '')
        auth_source = settings.DATABASES['default']['CLIENT'].get('authSource', 'admin')
        db_name = settings.DATABASES['default']['NAME']
        
        print(f"Connecting to MongoDB at {host}:{port}, database: {db_name}")
        
        # Create connection options
        connection_kwargs = {
            'host': host,
            'port': port,
        }
        
        # Add authentication if credentials are provided
        if username and password:
            print(f"Using authentication with username: {username}, authSource: {auth_source}")
            connection_kwargs.update({
                'username': username,
                'password': password,
                'authSource': auth_source
            })
        else:
            print("No authentication credentials provided")
        
        # Connect to MongoDB with timeout
        start_time = time.time()
        client = MongoClient(**connection_kwargs, serverSelectionTimeoutMS=5000)
        
        # Test the connection
        client.admin.command('ping')
        
        # Get database
        db = client[db_name]
        
        # Print collections
        collections = db.list_collection_names()
        print(f"\nConnection successful! ({time.time() - start_time:.2f}s)")
        print(f"Available collections in {db_name}:")
        
        if collections:
            for collection in collections:
                count = db[collection].count_documents({})
                print(f"- {collection}: {count} documents")
        else:
            print("No collections found. Run init_mongodb.py to create the required collections.")
        
        return True
        
    except Exception as e:
        print(f"\nMongoDB connection failed: {str(e)}")
        print("\nTroubleshooting tips:")
        print("1. Make sure MongoDB is installed and running")
        print("2. Check if the connection URL is correct")
        print("3. Verify authentication credentials if using authentication")
        print("4. Ensure the MongoDB port is not blocked by a firewall")
        return False

if __name__ == "__main__":
    check_mongodb_connection()