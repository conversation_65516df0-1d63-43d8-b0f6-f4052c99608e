"""
Script to check user data in MongoDB
"""

import os
import sys
import django
from bson import ObjectId

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from app.utils.mongodb_utils import get_db

def check_user_data(username=None):
    """Check user data in MongoDB"""
    db = get_db()
    
    # Get all collections
    collections = {
        'users': db.users,
        'journal_entries': db.journal_entries,
        'journal_streaks': db.journal_streaks,
        'journal_reminders': db.journal_reminders
    }
    
    # If username is provided, find that specific user
    if username:
        user = collections['users'].find_one({'username': username})
        if user:
            print(f"\nUser found: {username} (ID: {user['django_id']})")
            
            # Find user's journal entries
            entries = list(collections['journal_entries'].find({'user_id': user['django_id']}))
            print(f"Journal entries: {len(entries)}")
            
            # Find user's streak
            streak = collections['journal_streaks'].find_one({'user_id': user['django_id']})
            if streak:
                print(f"Current streak: {streak.get('current_streak', 0)}")
                print(f"Longest streak: {streak.get('longest_streak', 0)}")
            
            # Find user's reminders
            reminders = list(collections['journal_reminders'].find({'user_id': user['django_id']}))
            print(f"Reminders: {len(reminders)}")
        else:
            print(f"User '{username}' not found.")
    else:
        # Print summary of all collections
        print("\nMongoDB Collections Summary:")
        for name, collection in collections.items():
            count = collection.count_documents({})
            print(f"- {name}: {count} documents")
        
        # List all usernames
        users = list(collections['users'].find({}, {'username': 1}))
        if users:
            print("\nUsers in database:")
            for user in users:
                print(f"- {user.get('username', 'Unknown')}")

if __name__ == "__main__":
    # Check if username was provided as command line argument
    if len(sys.argv) > 1:
        check_user_data(sys.argv[1])
    else:
        check_user_data()
        print("\nTip: You can check a specific user by providing their username as an argument:")
        print("python scripts/check_user_data.py username")