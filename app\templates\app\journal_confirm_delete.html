{% extends 'app/base.html' %}
{% load static %}

{% block content %}
<div class="confirm-delete-container">
    <div class="confirm-delete-card">
        <h2>Delete Journal Entry</h2>
        <p>Are you sure you want to delete the journal entry "{{ entry.title }}"?</p>
        <p class="warning">This action cannot be undone.</p>
        
        <div class="entry-preview">
            <h3>{{ entry.title }}</h3>
            <div class="entry-date">{{ entry.created_at|date:"F j, Y" }}</div>
            <p>{{ entry.content|truncatewords:30 }}</p>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="form-actions">
                <button type="submit" class="button danger">
                    <i class="fas fa-trash animated-icon"></i> Delete
                </button>
                <a href="{% url 'journal_detail' pk=entry._id %}" class="button secondary">
                    <i class="fas fa-times animated-icon"></i> Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<style>
    .confirm-delete-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 70vh;
    }
    
    .confirm-delete-card {
        background: var(--card-gradient);
        border-radius: 12px;
        padding: 2rem;
        width: 100%;
        max-width: 600px;
        box-shadow: var(--shadow);
        border: 1px solid var(--border-color);
        text-align: center;
    }
    
    .confirm-delete-card h2 {
        color: var(--accent-light);
        margin-bottom: 1rem;
    }
    
    .warning {
        color: #e74c3c;
        font-weight: bold;
        margin-bottom: 1.5rem;
    }
    
    .entry-preview {
        background: rgba(0,0,0,0.2);
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        text-align: left;
    }
    
    .entry-preview h3 {
        margin-bottom: 0.5rem;
    }
    
    .entry-preview .entry-date {
        color: var(--accent-dark);
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }
    
    .form-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }
</style>
{% endblock %}
