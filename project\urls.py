"""project URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from app import views
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', views.home, name='home'),
    path('signup/', views.signup_view, name='signup'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('journal/', views.journal_list, name='journal_list'),
    path('journal/search/', views.journal_search, name='journal_search'),
    path('journal/new/', views.journal_new, name='journal_new'),
    path('journal/<str:pk>/', views.journal_detail, name='journal_detail'),
    path('journal/<str:pk>/edit/', views.journal_edit, name='journal_edit'),
    path('journal/<str:pk>/delete/', views.journal_delete, name='journal_delete'),
    path('calendar/', views.calendar_view, name='calendar_view'),
    path('journal/date/<str:date_str>/', views.journal_by_date, name='journal_by_date'),
    path('profile/', views.profile_view, name='profile'),
    # Reminder URLs
    path('reminders/', views.reminder_list, name='reminder_list'),
    path('reminders/new/', views.reminder_create, name='reminder_create'),
    path('reminders/<str:pk>/edit/', views.reminder_edit, name='reminder_edit'),
    path('reminders/<str:pk>/delete/', views.reminder_delete, name='reminder_delete'),
    path('reminders/<str:pk>/toggle/', views.reminder_toggle, name='reminder_toggle'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
