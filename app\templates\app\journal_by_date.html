{% extends 'app/base.html' %}
{% load static %}

{% block content %}
<div class="journal-header">
    <h2>Journal Entries for {{ formatted_date }}</h2>
    <a href="{% url 'calendar_view' %}" class="button secondary">
        <i class="fas fa-calendar-alt animated-icon"></i> Back to Calendar
    </a>
</div>

<div class="entry-list">
    {% if entries %}
        {% for entry in entries %}
        <div class="entry-card">
            {% if entry.image %}
            <div class="entry-image">
                <img src="/media/{{ entry.image }}" alt="{{ entry.title }}">
            </div>
            {% endif %}
            <div class="entry-content">
                <h3 class="entry-title">{{ entry.title }}</h3>
                <div class="entry-date">
                    <i class="far fa-calendar-alt animated-icon"></i> {{ entry.created_at|date:"F j, Y" }}
                </div>
                <p class="entry-preview">{{ entry.content|truncatewords:20 }}</p>
                <div class="entry-actions">
                    <a href="{% url 'journal_detail' pk=entry._id %}" class="button"><i class="fas fa-book-reader animated-icon"></i> Read More</a>
                </div>
                {% if entry.tags %}
                <div class="entry-tags">
                    {% for tag in entry.tags.split %}
                    <span class="tag">{{ tag }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-calendar-day"></i>
            </div>
            <h3>No Entries for {{ formatted_date }}</h3>
            <p>You haven't created any journal entries for this date.</p>
            <div class="empty-actions">
                <a href="{% url 'journal_new' %}" class="button pulse-button">
                    <i class="fas fa-plus animated-icon"></i> Create an Entry
                </a>
                <a href="{% url 'calendar_view' %}" class="button secondary">
                    <i class="fas fa-calendar-alt animated-icon"></i> Back to Calendar
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}




