from pymongo import MongoClient
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

def get_mongodb_client():
    """Get a MongoDB client connection"""
    try:
        # Get connection parameters from settings
        host = settings.DATABASES['default']['CLIENT']['host']
        port = settings.DATABASES['default']['CLIENT']['port']
        username = settings.DATABASES['default']['CLIENT'].get('username', '')
        password = settings.DATABASES['default']['CLIENT'].get('password', '')
        auth_source = settings.DATABASES['default']['CLIENT'].get('authSource', 'admin')
        
        # Create connection options
        connection_kwargs = {
            'host': host,
            'port': port,
        }
        
        # Add authentication if credentials are provided
        if username and password:
            connection_kwargs.update({
                'username': username,
                'password': password,
                'authSource': auth_source
            })
        
        # Connect to MongoDB
        client = MongoClient(**connection_kwargs)
        
        # Test the connection
        client.admin.command('ping')
        logger.info("MongoDB connection successful")
        return client
    except Exception as e:
        logger.error(f"MongoDB connection failed: {str(e)}")
        raise

def get_db():
    """Get the MongoDB database"""
    client = get_mongodb_client()
    db_name = settings.DATABASES['default']['NAME']
    return client[db_name]

