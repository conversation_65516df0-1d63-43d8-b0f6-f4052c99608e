{% extends 'app/base.html' %}
{% load static %}

{% block content %}
<div class="profile-container">
    <div class="profile-card">
        <div class="profile-header">
            <div class="profile-avatar">
                <i class="fas fa-user-circle"></i>
            </div>
            <div class="profile-info">
                <h2>{{ user.username }}</h2>
                <p>Member since: {{ user.date_joined|date:"F j, Y" }}</p>
            </div>
        </div>
        
        <div class="profile-stats">
            <div class="stat-card">
                <div class="stat-value">{{ total_entries }}</div>
                <div class="stat-label">Total Entries</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value">{{ streak_data.current_streak|default:"0" }}</div>
                <div class="stat-label">Current Streak</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value">{{ streak_data.longest_streak|default:"0" }}</div>
                <div class="stat-label">Longest Streak</div>
            </div>
        </div>
        
        <h3>Journal Activity</h3>
        <div class="activity-chart">
            {% for month in entries_by_month %}
            <div class="activity-bar">
                <div class="bar-label">{{ month.month }}</div>
                <div class="bar-container">
                    <div class="bar" style="height: {% if month.count > 0 %}{{ month.count|add:5 }}{% else %}0{% endif %}px;"></div>
                </div>
                <div class="bar-value">{{ month.count }}</div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<style>
    .activity-chart {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        height: 200px;
        margin-top: 20px;
    }
    
    .activity-bar {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: calc(100% / 12);
    }
    
    .bar-label {
        font-size: 0.8rem;
        margin-bottom: 5px;
        transform: rotate(-45deg);
        white-space: nowrap;
    }
    
    .bar-container {
        height: 150px;
        display: flex;
        align-items: flex-end;
    }
    
    .bar {
        width: 15px;
        background: var(--accent-light);
        border-radius: 3px 3px 0 0;
        transition: height 0.3s ease;
    }
    
    .bar-value {
        margin-top: 5px;
        font-size: 0.8rem;
    }
    
    @media (max-width: 768px) {
        .activity-chart {
            overflow-x: auto;
            padding-bottom: 10px;
        }
        
        .activity-bar {
            min-width: 40px;
        }
    }
</style>
{% endblock %}


