"""
Script to check MongoDB metadata consistency (for sharded clusters)
"""

import os
import sys
import django
from pymongo import MongoClient

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from app.utils.mongodb_utils import get_mongodb_client

def check_metadata_consistency():
    """Check MongoDB metadata consistency"""
    client = get_mongodb_client()
    
    print("Checking MongoDB metadata consistency...")
    
    try:
        # This command only works on mongos for sharded clusters
        result = client.admin.command('checkMetadataConsistency', 1)
        
        if 'documents' in result and result['documents']:
            print("\nInconsistencies found:")
            for doc in result['documents']:
                print(f"- Type: {doc.get('type')}")
                print(f"  Description: {doc.get('description')}")
                print(f"  Details: {doc.get('details')}")
                print()
        else:
            print("\nNo metadata inconsistencies found.")
            
    except Exception as e:
        print(f"\nFailed to check metadata consistency: {str(e)}")
        print("Note: This command only works on sharded clusters through mongos.")

if __name__ == "__main__":
    check_metadata_consistency()