{% extends 'app/base.html' %}
{% load static %}

{% block content %}
<div class="form-container">
    <h2>{% if form.instance.pk %}Edit Journal Entry{% else %}New Journal Entry{% endif %}</h2>
    
    <form method="POST" enctype="multipart/form-data" id="journalForm">
        {% csrf_token %}
        
        <div class="form-group">
            <label for="{{ form.title.id_for_label }}">Title</label>
            {{ form.title }}
            {% if form.title.errors %}
                <div class="form-error">{{ form.title.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.entry_date.id_for_label }}">Entry Date</label>
            {{ form.entry_date }}
            <div class="form-help">{{ form.entry_date.help_text }}</div>
            {% if form.entry_date.errors %}
                <div class="form-error">{{ form.entry_date.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.content.id_for_label }}">Content</label>
            {{ form.content }}
            {% if form.content.errors %}
                <div class="form-error">{{ form.content.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.category.id_for_label }}">Category</label>
            {{ form.category }}
            {% if form.category.errors %}
                <div class="form-error">{{ form.category.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.tags.id_for_label }}">Tags</label>
            {{ form.tags }}
            <div class="form-help">{{ form.tags.help_text }}</div>
            {% if form.tags.errors %}
                <div class="form-error">{{ form.tags.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.image.id_for_label }}">Image</label>
            {{ form.image }}
            {% if form.image.errors %}
                <div class="form-error">{{ form.image.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-actions">
            <button type="submit" class="button primary-btn">
                <i class="fas fa-save animated-icon"></i> Save Entry
            </button>
            <a href="{% url 'journal_list' %}" class="button secondary" id="cancelBtn">
                <i class="fas fa-times-circle animated-icon"></i> Cancel
            </a>
        </div>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('journalForm');
        const cancelBtn = document.getElementById('cancelBtn');
        let formChanged = false;
        
        // Track form changes
        const formInputs = form.querySelectorAll('input, textarea, select');
        formInputs.forEach(input => {
            input.addEventListener('change', () => {
                formChanged = true;
            });
            input.addEventListener('keyup', () => {
                formChanged = true;
            });
        });
        
        // Cancel button click handler
        cancelBtn.addEventListener('click', function(e) {
            if (formChanged) {
                e.preventDefault();
                if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
                    window.location.href = "{% url 'journal_list' %}";
                }
            }
        });
    });
</script>
{% endblock %}