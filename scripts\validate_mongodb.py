"""
Script to validate MongoDB collections
"""

import os
import sys
import django
from pymongo import MongoClient

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from app.utils.mongodb_utils import get_db

def validate_collections():
    """Validate MongoDB collections"""
    db = get_db()
    
    print("Validating MongoDB collections...")
    
    # Get all collections
    collections = db.list_collection_names()
    
    for collection_name in collections:
        print(f"\nValidating collection: {collection_name}")
        
        # Run validation command
        result = db.command("validate", collection_name)
        
        # Print validation results
        if result.get("valid"):
            print(f"✓ Collection '{collection_name}' is valid")
        else:
            print(f"✗ Collection '{collection_name}' has issues:")
            print(result.get("errors", "Unknown error"))
        
        # Print some stats
        print(f"- Documents: {result.get('nrecords', 'unknown')}")
        print(f"- Size: {result.get('size', 'unknown')} bytes")

if __name__ == "__main__":
    validate_collections()