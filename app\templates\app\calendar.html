{% extends 'app/base.html' %}
{% load static %}

{% block content %}
<h2 class="page-title">Journal Calendar</h2>

<div class="classic-calendar">
    <div class="calendar-binding">
        <div class="binding-hole"></div>
        <div class="binding-hole"></div>
    </div>
    
    <div class="calendar-header">
        <button id="prev-month" class="calendar-nav-btn">
            <i class="fas fa-chevron-left"></i>
        </button>
        <h3 id="month-year">August</h3>
        <button id="next-month" class="calendar-nav-btn">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
    
    <div class="calendar-body">
        <div class="weekdays">
            <div class="weekday">Sun</div>
            <div class="weekday">Mon</div>
            <div class="weekday">Tue</div>
            <div class="weekday">Wed</div>
            <div class="weekday">Thu</div>
            <div class="weekday">Fri</div>
            <div class="weekday">Sat</div>
        </div>
        
        <div id="calendar-days" class="days"></div>
    </div>
</div>

<div class="calendar-actions">
    <a href="{% url 'journal_list' %}" class="button secondary">
        <i class="fas fa-list animated-icon"></i> List View
    </a>
    <a href="{% url 'journal_new' %}" class="button pulse-button">
        <i class="fas fa-plus animated-icon"></i> New Entry
    </a>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calendar data from Django
        const calendarData = {{ calendar_data|safe }};
        
        let currentDate = new Date();
        let currentMonth = currentDate.getMonth();
        let currentYear = currentDate.getFullYear();
        
        // Elements
        const monthYearElement = document.getElementById('month-year');
        const calendarDaysElement = document.getElementById('calendar-days');
        const prevMonthButton = document.getElementById('prev-month');
        const nextMonthButton = document.getElementById('next-month');
        
        // Event listeners for navigation
        prevMonthButton.addEventListener('click', () => {
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            renderCalendar();
        });
        
        nextMonthButton.addEventListener('click', () => {
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            renderCalendar();
        });
        
        // Render the calendar
        function renderCalendar() {
            // Update month and year display
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 
                               'July', 'August', 'September', 'October', 'November', 'December'];
            monthYearElement.textContent = `${monthNames[currentMonth]} ${currentYear}`;
            
            // Clear previous days
            calendarDaysElement.innerHTML = '';
            
            // Get first day of month and total days
            const firstDay = new Date(currentYear, currentMonth, 1).getDay();
            const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
            
            // Previous month's days
            const prevMonthDays = new Date(currentYear, currentMonth, 0).getDate();
            for (let i = firstDay - 1; i >= 0; i--) {
                const dayElement = document.createElement('div');
                dayElement.classList.add('day', 'other-month');
                dayElement.textContent = prevMonthDays - i;
                calendarDaysElement.appendChild(dayElement);
            }
            
            // Current month's days
            const today = new Date();
            for (let i = 1; i <= daysInMonth; i++) {
                const dayElement = document.createElement('div');
                dayElement.classList.add('day');
                dayElement.textContent = i;
                
                // Check if it's today
                if (currentYear === today.getFullYear() && 
                    currentMonth === today.getMonth() && 
                    i === today.getDate()) {
                    dayElement.classList.add('today');
                }
                
                // Format date string to match the format in calendarData
                const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
                
                // Check if there are entries for this day
                if (calendarData[dateStr] && calendarData[dateStr].length > 0) {
                    dayElement.classList.add('has-entries');
                }
                
                // Add click event to navigate to entries for that day
                dayElement.addEventListener('click', () => {
                    window.location.href = `/journal/date/${dateStr}/`;
                });
                
                calendarDaysElement.appendChild(dayElement);
            }
            
            // Next month's days to fill the grid
            const totalDaysDisplayed = calendarDaysElement.childElementCount;
            const daysToAdd = 42 - totalDaysDisplayed; // 6 rows of 7 days
            
            for (let i = 1; i <= daysToAdd; i++) {
                const dayElement = document.createElement('div');
                dayElement.classList.add('day', 'other-month');
                dayElement.textContent = i;
                calendarDaysElement.appendChild(dayElement);
            }
        }
        
        // Initial render
        renderCalendar();
    });
</script>
{% endblock %}





