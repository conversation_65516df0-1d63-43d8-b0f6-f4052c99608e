{% extends 'app/base.html' %}
{% load static %}
{% load app_filters %}

{% block content %}
<div class="simple-calendar-page">
    <div class="calendar-container">
        <!-- Simple Calendar Card -->
        <div class="calendar-card">
            <!-- Calendar Header -->
            <div class="calendar-header">
                <div class="month-year">
                    <div class="month">{{ month_name|upper }}</div>
                    <div class="year">{{ current_year }}</div>
                </div>
            </div>

            <!-- Calendar Navigation -->
            <div class="calendar-nav">
                <button id="prev-month" class="nav-arrow">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button id="next-month" class="nav-arrow">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <!-- Calendar Body -->
            <div class="calendar-body">
                <!-- Weekday Headers -->
                <div class="weekdays">
                    <div class="weekday">MON</div>
                    <div class="weekday">TUE</div>
                    <div class="weekday">WED</div>
                    <div class="weekday">THU</div>
                    <div class="weekday">FRI</div>
                    <div class="weekday">SAT</div>
                    <div class="weekday">SUN</div>
                </div>

                <!-- Calendar Days Grid -->
                <div id="calendar-grid" class="calendar-grid">
                    <!-- Days will be generated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="calendar-actions">
        <a href="{% url 'journal_new' %}" class="action-button primary">
            <i class="fas fa-plus"></i> New Entry
        </a>
        <a href="{% url 'journal_list' %}" class="action-button secondary">
            <i class="fas fa-list"></i> View All
        </a>
    </div>
</div>



<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calendar data from Django
        const calendarData = {{ calendar_data|safe }};

        // Current state
        let currentView = '{{ view_type }}';
        let currentDate = new Date('{{ current_date|date:"Y-m-d" }}');
        let selectedDate = null;

        // Initialize calendar
        initializeCalendar();

        function initializeCalendar() {
            setupEventListeners();
            renderCurrentView();
        }

        function setupEventListeners() {
            // View switching
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const view = btn.dataset.view;
                    switchView(view);
                });
            });

            // Navigation
            document.getElementById('prev-period').addEventListener('click', navigatePrevious);
            document.getElementById('next-period').addEventListener('click', navigateNext);
            document.getElementById('today-btn').addEventListener('click', goToToday);

            // Date picker
            document.getElementById('date-picker-input').addEventListener('change', (e) => {
                currentDate = new Date(e.target.value);
                updateURL();
                renderCurrentView();
            });

            // Quick actions
            document.getElementById('quick-add-btn').addEventListener('click', openQuickAddModal);
            document.getElementById('search-btn').addEventListener('click', openSearchModal);

            // Modal controls
            setupModalControls();
        }

        function switchView(view) {
            currentView = view;

            // Update active button
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.view === view);
            });

            // Update active view
            document.querySelectorAll('.calendar-view').forEach(viewEl => {
                viewEl.classList.toggle('active', viewEl.id === view + '-view');
            });

            updateURL();
            renderCurrentView();
        }

        function navigatePrevious() {
            switch(currentView) {
                case 'month':
                    currentDate.setMonth(currentDate.getMonth() - 1);
                    break;
                case 'week':
                    currentDate.setDate(currentDate.getDate() - 7);
                    break;
                case 'day':
                    currentDate.setDate(currentDate.getDate() - 1);
                    break;
            }
            updateURL();
            renderCurrentView();
        }

        function navigateNext() {
            switch(currentView) {
                case 'month':
                    currentDate.setMonth(currentDate.getMonth() + 1);
                    break;
                case 'week':
                    currentDate.setDate(currentDate.getDate() + 7);
                    break;
                case 'day':
                    currentDate.setDate(currentDate.getDate() + 1);
                    break;
            }
            updateURL();
            renderCurrentView();
        }

        function goToToday() {
            currentDate = new Date();
            updateURL();
            renderCurrentView();
        }

        function updateURL() {
            const url = new URL(window.location);
            url.searchParams.set('view', currentView);
            url.searchParams.set('date', currentDate.toISOString().split('T')[0]);
            window.history.replaceState({}, '', url);

            // Update date picker
            document.getElementById('date-picker-input').value = currentDate.toISOString().split('T')[0];
        }

        function renderCurrentView() {
            updatePeriodText();

            switch(currentView) {
                case 'month':
                    renderMonthView();
                    break;
                case 'week':
                    renderWeekView();
                    break;
                case 'day':
                    renderDayView();
                    break;
            }
        }

        function updatePeriodText() {
            const periodText = document.getElementById('current-period-text');
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                               'July', 'August', 'September', 'October', 'November', 'December'];

            switch(currentView) {
                case 'month':
                    periodText.textContent = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
                    break;
                case 'week':
                    const weekStart = new Date(currentDate);
                    weekStart.setDate(currentDate.getDate() - currentDate.getDay());
                    const weekEnd = new Date(weekStart);
                    weekEnd.setDate(weekStart.getDate() + 6);
                    periodText.textContent = `${weekStart.toLocaleDateString()} - ${weekEnd.toLocaleDateString()}`;
                    break;
                case 'day':
                    periodText.textContent = currentDate.toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                    break;
            }
        }

        function renderMonthView() {
            const grid = document.getElementById('month-calendar-grid');
            grid.innerHTML = '';

            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();
            const firstDay = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            const today = new Date();

            // Previous month's days
            const prevMonthDays = new Date(year, month, 0).getDate();
            for (let i = firstDay - 1; i >= 0; i--) {
                const dayEl = createDayElement(prevMonthDays - i, true, new Date(year, month - 1, prevMonthDays - i));
                grid.appendChild(dayEl);
            }

            // Current month's days
            for (let day = 1; day <= daysInMonth; day++) {
                const date = new Date(year, month, day);
                const isToday = date.toDateString() === today.toDateString();
                const dayEl = createDayElement(day, false, date, isToday);
                grid.appendChild(dayEl);
            }

            // Next month's days
            const totalCells = grid.children.length;
            const remainingCells = 42 - totalCells;
            for (let day = 1; day <= remainingCells; day++) {
                const dayEl = createDayElement(day, true, new Date(year, month + 1, day));
                grid.appendChild(dayEl);
            }
        }

        function createDayElement(dayNumber, isOtherMonth, date, isToday = false) {
            const dayEl = document.createElement('div');
            dayEl.className = `calendar-day ${isOtherMonth ? 'other-month' : ''} ${isToday ? 'today' : ''}`;

            const dateStr = date.toISOString().split('T')[0];
            const dayData = calendarData[dateStr];

            // Day number
            const numberEl = document.createElement('span');
            numberEl.className = 'day-number';
            numberEl.textContent = dayNumber;
            dayEl.appendChild(numberEl);

            // Events indicator
            if (dayData && dayData.total_count > 0) {
                dayEl.classList.add('has-events');

                const indicatorEl = document.createElement('div');
                indicatorEl.className = 'events-indicator';

                if (dayData.entries.length > 0) {
                    const entryDot = document.createElement('span');
                    entryDot.className = 'event-dot entry-dot';
                    entryDot.title = `${dayData.entries.length} journal entries`;
                    indicatorEl.appendChild(entryDot);
                }

                if (dayData.reminders.length > 0) {
                    const reminderDot = document.createElement('span');
                    reminderDot.className = 'event-dot reminder-dot';
                    reminderDot.title = `${dayData.reminders.length} reminders`;
                    indicatorEl.appendChild(reminderDot);
                }

                dayEl.appendChild(indicatorEl);
            }

            // Click handler
            dayEl.addEventListener('click', () => {
                selectedDate = date;
                showDayEvents(date, dayData);
            });

            return dayEl;
        }

        function renderWeekView() {
            const grid = document.getElementById('week-calendar-grid');
            grid.innerHTML = '';

            // Create time slots and week days
            for (let hour = 0; hour < 24; hour++) {
                const timeSlot = document.createElement('div');
                timeSlot.className = 'time-slot';

                const timeLabel = document.createElement('div');
                timeLabel.className = 'time-label';
                timeLabel.textContent = `${hour.toString().padStart(2, '0')}:00`;
                timeSlot.appendChild(timeLabel);

                for (let day = 0; day < 7; day++) {
                    const daySlot = document.createElement('div');
                    daySlot.className = 'day-slot';

                    const slotDate = new Date(currentDate);
                    slotDate.setDate(currentDate.getDate() - currentDate.getDay() + day);
                    slotDate.setHours(hour, 0, 0, 0);

                    const dateStr = slotDate.toISOString().split('T')[0];
                    const dayData = calendarData[dateStr];

                    if (dayData) {
                        // Add events for this time slot
                        dayData.reminders.forEach(reminder => {
                            const reminderTime = new Date(`${dateStr}T${reminder.time}`);
                            if (reminderTime.getHours() === hour) {
                                const eventEl = document.createElement('div');
                                eventEl.className = 'week-event reminder-event';
                                eventEl.textContent = reminder.title;
                                eventEl.addEventListener('click', () => showEventDetails(reminder));
                                daySlot.appendChild(eventEl);
                            }
                        });
                    }

                    timeSlot.appendChild(daySlot);
                }

                grid.appendChild(timeSlot);
            }
        }

        function renderDayView() {
            const content = document.getElementById('day-calendar-content');
            content.innerHTML = '';

            const dateStr = currentDate.toISOString().split('T')[0];
            const dayData = calendarData[dateStr];

            if (!dayData || dayData.total_count === 0) {
                content.innerHTML = `
                    <div class="no-events">
                        <i class="fas fa-calendar-day"></i>
                        <p>No events for this day</p>
                        <button class="btn btn-primary" onclick="openQuickAddModal('${dateStr}')">
                            <i class="fas fa-plus"></i> Add Event
                        </button>
                    </div>
                `;
                return;
            }

            // Journal entries
            if (dayData.entries.length > 0) {
                const entriesSection = document.createElement('div');
                entriesSection.className = 'day-section';
                entriesSection.innerHTML = `
                    <h4><i class="fas fa-book"></i> Journal Entries (${dayData.entries.length})</h4>
                `;

                dayData.entries.forEach(entry => {
                    const entryEl = document.createElement('div');
                    entryEl.className = 'day-event entry-event';
                    entryEl.innerHTML = `
                        <div class="event-time">${entry.created_at}</div>
                        <div class="event-title">${entry.title}</div>
                        <div class="event-content">${entry.content}</div>
                        ${entry.tags.length > 0 ? `<div class="event-tags">${entry.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>` : ''}
                    `;
                    entryEl.addEventListener('click', () => showEventDetails(entry));
                    entriesSection.appendChild(entryEl);
                });

                content.appendChild(entriesSection);
            }

            // Reminders
            if (dayData.reminders.length > 0) {
                const remindersSection = document.createElement('div');
                remindersSection.className = 'day-section';
                remindersSection.innerHTML = `
                    <h4><i class="fas fa-bell"></i> Reminders (${dayData.reminders.length})</h4>
                `;

                dayData.reminders.forEach(reminder => {
                    const reminderEl = document.createElement('div');
                    reminderEl.className = `day-event reminder-event ${reminder.is_completed ? 'completed' : ''}`;
                    reminderEl.innerHTML = `
                        <div class="event-time">${reminder.time}</div>
                        <div class="event-title">${reminder.title}</div>
                        ${reminder.description ? `<div class="event-content">${reminder.description}</div>` : ''}
                        <div class="event-status">
                            <span class="status-badge ${reminder.is_completed ? 'completed' : 'pending'}">
                                ${reminder.is_completed ? 'Completed' : 'Pending'}
                            </span>
                        </div>
                    `;
                    reminderEl.addEventListener('click', () => showEventDetails(reminder));
                    remindersSection.appendChild(reminderEl);
                });

                content.appendChild(remindersSection);
            }
        }

        // Modal and event handling functions
        function setupModalControls() {
            // Close modal handlers
            document.querySelectorAll('.modal-close').forEach(btn => {
                btn.addEventListener('click', closeModals);
            });

            // Click outside modal to close
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) closeModals();
                });
            });

            // Tab switching in quick add modal
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const tab = e.target.dataset.tab;
                    switchTab(tab);
                });
            });

            // Quick save handler
            document.getElementById('quick-save-btn').addEventListener('click', handleQuickSave);
        }

        function openQuickAddModal(dateStr = null) {
            const modal = document.getElementById('quick-add-modal');
            const date = dateStr || currentDate.toISOString().split('T')[0];

            document.getElementById('entry-date').value = date;
            document.getElementById('reminder-date').value = date;

            modal.style.display = 'block';
        }

        function openSearchModal() {
            const modal = document.getElementById('search-modal');
            modal.style.display = 'block';
        }

        function closeModals() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.style.display = 'none';
            });
        }

        function switchTab(tab) {
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.tab === tab);
            });

            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.toggle('active', content.id === tab + '-tab');
            });
        }

        function showDayEvents(date, dayData) {
            if (!dayData || dayData.total_count === 0) {
                openQuickAddModal(date.toISOString().split('T')[0]);
                return;
            }

            // Switch to day view for the selected date
            currentDate = new Date(date);
            switchView('day');
        }

        function showEventDetails(event) {
            const modal = document.getElementById('event-modal');
            const title = document.getElementById('modal-title');
            const content = document.getElementById('modal-content');

            title.textContent = event.title;

            if (event.type === 'entry') {
                content.innerHTML = `
                    <div class="event-details">
                        <div class="detail-row">
                            <strong>Type:</strong> Journal Entry
                        </div>
                        <div class="detail-row">
                            <strong>Created:</strong> ${event.created_at}
                        </div>
                        <div class="detail-row">
                            <strong>Content:</strong>
                            <div class="content-preview">${event.content}</div>
                        </div>
                        ${event.tags.length > 0 ? `
                            <div class="detail-row">
                                <strong>Tags:</strong>
                                <div class="tags-list">${event.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>
                            </div>
                        ` : ''}
                    </div>
                `;
            } else {
                content.innerHTML = `
                    <div class="event-details">
                        <div class="detail-row">
                            <strong>Type:</strong> Reminder
                        </div>
                        <div class="detail-row">
                            <strong>Time:</strong> ${event.time}
                        </div>
                        <div class="detail-row">
                            <strong>Status:</strong>
                            <span class="status-badge ${event.is_completed ? 'completed' : 'pending'}">
                                ${event.is_completed ? 'Completed' : 'Pending'}
                            </span>
                        </div>
                        ${event.description ? `
                            <div class="detail-row">
                                <strong>Description:</strong>
                                <div class="content-preview">${event.description}</div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            modal.style.display = 'block';
        }

        function handleQuickSave() {
            const activeTab = document.querySelector('.tab-btn.active').dataset.tab;

            if (activeTab === 'entry') {
                // Handle journal entry creation
                const form = document.getElementById('quick-entry-form');
                const formData = new FormData(form);

                // You would typically send this to your backend
                console.log('Creating journal entry:', Object.fromEntries(formData));

                // For now, redirect to the full form
                const date = formData.get('date');
                window.location.href = `/journal/new/?date=${date}`;
            } else {
                // Handle reminder creation
                const form = document.getElementById('quick-reminder-form');
                const formData = new FormData(form);

                console.log('Creating reminder:', Object.fromEntries(formData));

                // For now, redirect to the full form
                window.location.href = '/reminders/new/';
            }
        }

        // Global functions for inline event handlers
        window.openQuickAddModal = openQuickAddModal;
        window.showEventDetails = showEventDetails;
    });
</script>
{% endblock %}





