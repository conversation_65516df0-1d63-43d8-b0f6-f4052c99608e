{% extends 'app/base.html' %}
{% load static %}
{% load app_filters %}

{% block content %}
<div class="calendar-page">
    <div class="calendar-header-info">
        <h2 class="page-title">Journal Calendar</h2>
        <div class="calendar-stats">
            <div class="stat-item">
                <i class="fas fa-calendar-day"></i>
                <span>{{ entries_this_month }} entries this month</span>
            </div>
            <div class="stat-item">
                <i class="fas fa-book"></i>
                <span>{{ total_entries }} total entries</span>
            </div>
        </div>
    </div>

    <div class="calendar-controls">
        <button id="today-btn" class="button secondary">
            <i class="fas fa-calendar-check"></i> Today
        </button>
        <div class="month-year-selector">
            <select id="month-select">
                <option value="1" {% if current_month == 1 %}selected{% endif %}>January</option>
                <option value="2" {% if current_month == 2 %}selected{% endif %}>February</option>
                <option value="3" {% if current_month == 3 %}selected{% endif %}>March</option>
                <option value="4" {% if current_month == 4 %}selected{% endif %}>April</option>
                <option value="5" {% if current_month == 5 %}selected{% endif %}>May</option>
                <option value="6" {% if current_month == 6 %}selected{% endif %}>June</option>
                <option value="7" {% if current_month == 7 %}selected{% endif %}>July</option>
                <option value="8" {% if current_month == 8 %}selected{% endif %}>August</option>
                <option value="9" {% if current_month == 9 %}selected{% endif %}>September</option>
                <option value="10" {% if current_month == 10 %}selected{% endif %}>October</option>
                <option value="11" {% if current_month == 11 %}selected{% endif %}>November</option>
                <option value="12" {% if current_month == 12 %}selected{% endif %}>December</option>
            </select>
            <select id="year-select">
                {% for year in "2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030"|split:"," %}
                    <option value="{{ year }}" {% if current_year|stringformat:"s" == year %}selected{% endif %}>{{ year }}</option>
                {% endfor %}
            </select>
        </div>
    </div>

    <div class="classic-calendar">
        <div class="calendar-binding">
            <div class="binding-hole"></div>
            <div class="binding-hole"></div>
        </div>

        <div class="calendar-header">
            <button id="prev-month" class="calendar-nav-btn">
                <i class="fas fa-chevron-left"></i>
            </button>
            <h3 id="month-year">{{ month_name }} {{ current_year }}</h3>
            <button id="next-month" class="calendar-nav-btn">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>

        <div class="calendar-body">
            <div class="weekdays">
                <div class="weekday">Sun</div>
                <div class="weekday">Mon</div>
                <div class="weekday">Tue</div>
                <div class="weekday">Wed</div>
                <div class="weekday">Thu</div>
                <div class="weekday">Fri</div>
                <div class="weekday">Sat</div>
            </div>

            <div id="calendar-days" class="days"></div>
        </div>
    </div>
</div>

<div class="calendar-actions">
    <a href="{% url 'journal_list' %}" class="button secondary">
        <i class="fas fa-list animated-icon"></i> List View
    </a>
    <a href="{% url 'journal_new' %}" class="button pulse-button">
        <i class="fas fa-plus animated-icon"></i> New Entry
    </a>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calendar data from Django
        const calendarData = {{ calendar_data|safe }};

        // Initialize with server-provided values
        let currentMonth = {{ current_month }} - 1; // JavaScript months are 0-based
        let currentYear = {{ current_year }};

        // Elements
        const monthYearElement = document.getElementById('month-year');
        const calendarDaysElement = document.getElementById('calendar-days');
        const prevMonthButton = document.getElementById('prev-month');
        const nextMonthButton = document.getElementById('next-month');
        const todayButton = document.getElementById('today-btn');
        const monthSelect = document.getElementById('month-select');
        const yearSelect = document.getElementById('year-select');

        // Event listeners for navigation
        prevMonthButton.addEventListener('click', () => {
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            updateURL();
            renderCalendar();
        });

        nextMonthButton.addEventListener('click', () => {
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            updateURL();
            renderCalendar();
        });

        // Today button
        todayButton.addEventListener('click', () => {
            const today = new Date();
            currentMonth = today.getMonth();
            currentYear = today.getFullYear();
            updateURL();
            renderCalendar();
        });

        // Month/Year selectors
        monthSelect.addEventListener('change', () => {
            currentMonth = parseInt(monthSelect.value) - 1;
            updateURL();
            renderCalendar();
        });

        yearSelect.addEventListener('change', () => {
            currentYear = parseInt(yearSelect.value);
            updateURL();
            renderCalendar();
        });

        // Update URL with current month/year
        function updateURL() {
            const url = new URL(window.location);
            url.searchParams.set('month', currentMonth + 1);
            url.searchParams.set('year', currentYear);
            window.history.replaceState({}, '', url);
        }

        // Render the calendar
        function renderCalendar() {
            // Update month and year display
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                               'July', 'August', 'September', 'October', 'November', 'December'];
            monthYearElement.textContent = `${monthNames[currentMonth]} ${currentYear}`;

            // Update selectors
            monthSelect.value = currentMonth + 1;
            yearSelect.value = currentYear;

            // Clear previous days
            calendarDaysElement.innerHTML = '';

            // Get first day of month and total days
            const firstDay = new Date(currentYear, currentMonth, 1).getDay();
            const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();

            // Previous month's days
            const prevMonthDays = new Date(currentYear, currentMonth, 0).getDate();
            for (let i = firstDay - 1; i >= 0; i--) {
                const dayElement = document.createElement('div');
                dayElement.classList.add('day', 'other-month');
                dayElement.textContent = prevMonthDays - i;
                calendarDaysElement.appendChild(dayElement);
            }

            // Current month's days
            const today = new Date();
            for (let i = 1; i <= daysInMonth; i++) {
                const dayElement = document.createElement('div');
                dayElement.classList.add('day');

                // Create day number element
                const dayNumber = document.createElement('span');
                dayNumber.classList.add('day-number');
                dayNumber.textContent = i;
                dayElement.appendChild(dayNumber);

                // Check if it's today
                if (currentYear === today.getFullYear() &&
                    currentMonth === today.getMonth() &&
                    i === today.getDate()) {
                    dayElement.classList.add('today');
                }

                // Format date string to match the format in calendarData
                const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;

                // Check if there are entries for this day
                if (calendarData[dateStr] && calendarData[dateStr].count > 0) {
                    dayElement.classList.add('has-entries');

                    // Add entry count indicator
                    const entryCount = document.createElement('span');
                    entryCount.classList.add('entry-count');
                    entryCount.textContent = calendarData[dateStr].count;
                    dayElement.appendChild(entryCount);

                    // Add tooltip with entry titles
                    const titles = calendarData[dateStr].entries.map(entry => entry.title).join(', ');
                    dayElement.title = `${calendarData[dateStr].count} entries: ${titles}`;
                }

                // Add click event to navigate to entries for that day
                dayElement.addEventListener('click', () => {
                    window.location.href = `/journal/date/${dateStr}/`;
                });

                calendarDaysElement.appendChild(dayElement);
            }

            // Next month's days to fill the grid
            const totalDaysDisplayed = calendarDaysElement.childElementCount;
            const daysToAdd = 42 - totalDaysDisplayed; // 6 rows of 7 days

            for (let i = 1; i <= daysToAdd; i++) {
                const dayElement = document.createElement('div');
                dayElement.classList.add('day', 'other-month');
                dayElement.textContent = i;
                calendarDaysElement.appendChild(dayElement);
            }
        }

        // Initial render
        renderCalendar();
    });
</script>
{% endblock %}





