{% extends 'app/base.html' %}
{% load static %}
{% load app_filters %}

{% block content %}
<div class="modern-calendar-page">
    <!-- Calendar Header with Stats and Controls -->
    <div class="calendar-header-section">
        <div class="calendar-title-area">
            <h1 class="calendar-title">
                <i class="fas fa-calendar-alt"></i>
                Personal Calendar
            </h1>
            <p class="calendar-subtitle">Track your journal entries and reminders</p>
        </div>

        <div class="calendar-stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-book-open"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ total_entries }}</div>
                    <div class="stat-label">Total Entries</div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ entries_this_month }}</div>
                    <div class="stat-label">This Month</div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-fire"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ current_streak }}</div>
                    <div class="stat-label">Current Streak</div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ total_reminders }}</div>
                    <div class="stat-label">Reminders</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Navigation and View Controls -->
    <div class="calendar-controls-section">
        <div class="view-controls">
            <div class="view-buttons">
                <button class="view-btn {% if view_type == 'month' %}active{% endif %}" data-view="month">
                    <i class="fas fa-calendar"></i> Month
                </button>
                <button class="view-btn {% if view_type == 'week' %}active{% endif %}" data-view="week">
                    <i class="fas fa-calendar-week"></i> Week
                </button>
                <button class="view-btn {% if view_type == 'day' %}active{% endif %}" data-view="day">
                    <i class="fas fa-calendar-day"></i> Day
                </button>
            </div>
        </div>

        <div class="navigation-controls">
            <button id="today-btn" class="nav-btn today-btn">
                <i class="fas fa-calendar-check"></i> Today
            </button>

            <div class="date-navigation">
                <button id="prev-period" class="nav-btn">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <div class="current-period">
                    <span id="current-period-text">{{ month_name }} {{ current_year }}</span>
                </div>

                <button id="next-period" class="nav-btn">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="date-picker">
                <input type="date" id="date-picker-input" value="{{ current_date|date:'Y-m-d' }}">
            </div>
        </div>

        <div class="action-controls">
            <button id="quick-add-btn" class="action-btn primary">
                <i class="fas fa-plus"></i> Quick Add
            </button>
            <button id="search-btn" class="action-btn secondary">
                <i class="fas fa-search"></i> Search
            </button>
        </div>
    </div>

    <!-- Main Calendar Display Area -->
    <div class="calendar-display-area">
        <!-- Month View -->
        <div id="month-view" class="calendar-view {% if view_type == 'month' %}active{% endif %}">
            <div class="month-calendar">
                <div class="month-header">
                    <div class="weekday-headers">
                        <div class="weekday-header">Sun</div>
                        <div class="weekday-header">Mon</div>
                        <div class="weekday-header">Tue</div>
                        <div class="weekday-header">Wed</div>
                        <div class="weekday-header">Thu</div>
                        <div class="weekday-header">Fri</div>
                        <div class="weekday-header">Sat</div>
                    </div>
                </div>
                <div id="month-calendar-grid" class="month-grid">
                    <!-- Calendar days will be generated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Week View -->
        <div id="week-view" class="calendar-view {% if view_type == 'week' %}active{% endif %}">
            <div class="week-calendar">
                <div class="week-header">
                    <div class="time-column"></div>
                    {% for date in week_dates %}
                    <div class="week-day-header">
                        <div class="day-name">{{ date|date:'D' }}</div>
                        <div class="day-number">{{ date|date:'j' }}</div>
                    </div>
                    {% endfor %}
                </div>
                <div id="week-calendar-grid" class="week-grid">
                    <!-- Week view will be generated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Day View -->
        <div id="day-view" class="calendar-view {% if view_type == 'day' %}active{% endif %}">
            <div class="day-calendar">
                <div class="day-header">
                    <h3 id="day-title">{{ current_date|date:'l, F j, Y' }}</h3>
                </div>
                <div id="day-calendar-content" class="day-content">
                    <!-- Day view content will be generated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Panel -->
    <div class="quick-actions-panel">
        <a href="{% url 'journal_new' %}" class="quick-action-btn journal-btn">
            <i class="fas fa-pen"></i>
            <span>New Entry</span>
        </a>
        <a href="{% url 'reminder_create' %}" class="quick-action-btn reminder-btn">
            <i class="fas fa-bell"></i>
            <span>Add Reminder</span>
        </a>
        <a href="{% url 'journal_list' %}" class="quick-action-btn list-btn">
            <i class="fas fa-list"></i>
            <span>View All</span>
        </a>
    </div>
</div>

<!-- Event Details Modal -->
<div id="event-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modal-title">Event Details</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div id="modal-content">
                <!-- Event details will be loaded here -->
            </div>
        </div>
        <div class="modal-footer">
            <button id="modal-edit-btn" class="btn btn-primary">Edit</button>
            <button id="modal-delete-btn" class="btn btn-danger">Delete</button>
            <button class="btn btn-secondary modal-close">Close</button>
        </div>
    </div>
</div>

<!-- Quick Add Modal -->
<div id="quick-add-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Quick Add</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div class="quick-add-tabs">
                <button class="tab-btn active" data-tab="entry">Journal Entry</button>
                <button class="tab-btn" data-tab="reminder">Reminder</button>
            </div>

            <div id="entry-tab" class="tab-content active">
                <form id="quick-entry-form">
                    <input type="hidden" id="entry-date" name="date">
                    <div class="form-group">
                        <label for="entry-title">Title</label>
                        <input type="text" id="entry-title" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="entry-content">Content</label>
                        <textarea id="entry-content" name="content" rows="4" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="entry-tags">Tags (comma-separated)</label>
                        <input type="text" id="entry-tags" name="tags">
                    </div>
                </form>
            </div>

            <div id="reminder-tab" class="tab-content">
                <form id="quick-reminder-form">
                    <input type="hidden" id="reminder-date" name="date">
                    <div class="form-group">
                        <label for="reminder-title">Title</label>
                        <input type="text" id="reminder-title" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="reminder-time">Time</label>
                        <input type="time" id="reminder-time" name="time" required>
                    </div>
                    <div class="form-group">
                        <label for="reminder-description">Description</label>
                        <textarea id="reminder-description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
        </div>
        <div class="modal-footer">
            <button id="quick-save-btn" class="btn btn-primary">Save</button>
            <button class="btn btn-secondary modal-close">Cancel</button>
        </div>
    </div>
</div>

<!-- Search Modal -->
<div id="search-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Search Calendar</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div class="search-form">
                <input type="text" id="search-input" placeholder="Search entries and reminders...">
                <div class="search-filters">
                    <label>
                        <input type="checkbox" id="filter-entries" checked> Journal Entries
                    </label>
                    <label>
                        <input type="checkbox" id="filter-reminders" checked> Reminders
                    </label>
                    <input type="date" id="search-from-date" placeholder="From date">
                    <input type="date" id="search-to-date" placeholder="To date">
                </div>
            </div>
            <div id="search-results" class="search-results">
                <!-- Search results will appear here -->
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calendar data from Django
        const calendarData = {{ calendar_data|safe }};

        // Current state
        let currentView = '{{ view_type }}';
        let currentDate = new Date('{{ current_date|date:"Y-m-d" }}');
        let selectedDate = null;

        // Initialize calendar
        initializeCalendar();

        function initializeCalendar() {
            setupEventListeners();
            renderCurrentView();
        }

        function setupEventListeners() {
            // View switching
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const view = btn.dataset.view;
                    switchView(view);
                });
            });

            // Navigation
            document.getElementById('prev-period').addEventListener('click', navigatePrevious);
            document.getElementById('next-period').addEventListener('click', navigateNext);
            document.getElementById('today-btn').addEventListener('click', goToToday);

            // Date picker
            document.getElementById('date-picker-input').addEventListener('change', (e) => {
                currentDate = new Date(e.target.value);
                updateURL();
                renderCurrentView();
            });

            // Quick actions
            document.getElementById('quick-add-btn').addEventListener('click', openQuickAddModal);
            document.getElementById('search-btn').addEventListener('click', openSearchModal);

            // Modal controls
            setupModalControls();
        }

        function switchView(view) {
            currentView = view;

            // Update active button
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.view === view);
            });

            // Update active view
            document.querySelectorAll('.calendar-view').forEach(viewEl => {
                viewEl.classList.toggle('active', viewEl.id === view + '-view');
            });

            updateURL();
            renderCurrentView();
        }

        function navigatePrevious() {
            switch(currentView) {
                case 'month':
                    currentDate.setMonth(currentDate.getMonth() - 1);
                    break;
                case 'week':
                    currentDate.setDate(currentDate.getDate() - 7);
                    break;
                case 'day':
                    currentDate.setDate(currentDate.getDate() - 1);
                    break;
            }
            updateURL();
            renderCurrentView();
        }

        function navigateNext() {
            switch(currentView) {
                case 'month':
                    currentDate.setMonth(currentDate.getMonth() + 1);
                    break;
                case 'week':
                    currentDate.setDate(currentDate.getDate() + 7);
                    break;
                case 'day':
                    currentDate.setDate(currentDate.getDate() + 1);
                    break;
            }
            updateURL();
            renderCurrentView();
        }

        function goToToday() {
            currentDate = new Date();
            updateURL();
            renderCurrentView();
        }

        function updateURL() {
            const url = new URL(window.location);
            url.searchParams.set('view', currentView);
            url.searchParams.set('date', currentDate.toISOString().split('T')[0]);
            window.history.replaceState({}, '', url);

            // Update date picker
            document.getElementById('date-picker-input').value = currentDate.toISOString().split('T')[0];
        }

        function renderCurrentView() {
            updatePeriodText();

            switch(currentView) {
                case 'month':
                    renderMonthView();
                    break;
                case 'week':
                    renderWeekView();
                    break;
                case 'day':
                    renderDayView();
                    break;
            }
        }

        function updatePeriodText() {
            const periodText = document.getElementById('current-period-text');
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                               'July', 'August', 'September', 'October', 'November', 'December'];

            switch(currentView) {
                case 'month':
                    periodText.textContent = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
                    break;
                case 'week':
                    const weekStart = new Date(currentDate);
                    weekStart.setDate(currentDate.getDate() - currentDate.getDay());
                    const weekEnd = new Date(weekStart);
                    weekEnd.setDate(weekStart.getDate() + 6);
                    periodText.textContent = `${weekStart.toLocaleDateString()} - ${weekEnd.toLocaleDateString()}`;
                    break;
                case 'day':
                    periodText.textContent = currentDate.toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                    break;
            }
        }

        function renderMonthView() {
            const grid = document.getElementById('month-calendar-grid');
            grid.innerHTML = '';

            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();
            const firstDay = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            const today = new Date();

            // Previous month's days
            const prevMonthDays = new Date(year, month, 0).getDate();
            for (let i = firstDay - 1; i >= 0; i--) {
                const dayEl = createDayElement(prevMonthDays - i, true, new Date(year, month - 1, prevMonthDays - i));
                grid.appendChild(dayEl);
            }

            // Current month's days
            for (let day = 1; day <= daysInMonth; day++) {
                const date = new Date(year, month, day);
                const isToday = date.toDateString() === today.toDateString();
                const dayEl = createDayElement(day, false, date, isToday);
                grid.appendChild(dayEl);
            }

            // Next month's days
            const totalCells = grid.children.length;
            const remainingCells = 42 - totalCells;
            for (let day = 1; day <= remainingCells; day++) {
                const dayEl = createDayElement(day, true, new Date(year, month + 1, day));
                grid.appendChild(dayEl);
            }
        }

        function createDayElement(dayNumber, isOtherMonth, date, isToday = false) {
            const dayEl = document.createElement('div');
            dayEl.className = `calendar-day ${isOtherMonth ? 'other-month' : ''} ${isToday ? 'today' : ''}`;

            const dateStr = date.toISOString().split('T')[0];
            const dayData = calendarData[dateStr];

            // Day number
            const numberEl = document.createElement('span');
            numberEl.className = 'day-number';
            numberEl.textContent = dayNumber;
            dayEl.appendChild(numberEl);

            // Events indicator
            if (dayData && dayData.total_count > 0) {
                dayEl.classList.add('has-events');

                const indicatorEl = document.createElement('div');
                indicatorEl.className = 'events-indicator';

                if (dayData.entries.length > 0) {
                    const entryDot = document.createElement('span');
                    entryDot.className = 'event-dot entry-dot';
                    entryDot.title = `${dayData.entries.length} journal entries`;
                    indicatorEl.appendChild(entryDot);
                }

                if (dayData.reminders.length > 0) {
                    const reminderDot = document.createElement('span');
                    reminderDot.className = 'event-dot reminder-dot';
                    reminderDot.title = `${dayData.reminders.length} reminders`;
                    indicatorEl.appendChild(reminderDot);
                }

                dayEl.appendChild(indicatorEl);
            }

            // Click handler
            dayEl.addEventListener('click', () => {
                selectedDate = date;
                showDayEvents(date, dayData);
            });

            return dayEl;
        }

        function renderWeekView() {
            const grid = document.getElementById('week-calendar-grid');
            grid.innerHTML = '';

            // Create time slots and week days
            for (let hour = 0; hour < 24; hour++) {
                const timeSlot = document.createElement('div');
                timeSlot.className = 'time-slot';

                const timeLabel = document.createElement('div');
                timeLabel.className = 'time-label';
                timeLabel.textContent = `${hour.toString().padStart(2, '0')}:00`;
                timeSlot.appendChild(timeLabel);

                for (let day = 0; day < 7; day++) {
                    const daySlot = document.createElement('div');
                    daySlot.className = 'day-slot';

                    const slotDate = new Date(currentDate);
                    slotDate.setDate(currentDate.getDate() - currentDate.getDay() + day);
                    slotDate.setHours(hour, 0, 0, 0);

                    const dateStr = slotDate.toISOString().split('T')[0];
                    const dayData = calendarData[dateStr];

                    if (dayData) {
                        // Add events for this time slot
                        dayData.reminders.forEach(reminder => {
                            const reminderTime = new Date(`${dateStr}T${reminder.time}`);
                            if (reminderTime.getHours() === hour) {
                                const eventEl = document.createElement('div');
                                eventEl.className = 'week-event reminder-event';
                                eventEl.textContent = reminder.title;
                                eventEl.addEventListener('click', () => showEventDetails(reminder));
                                daySlot.appendChild(eventEl);
                            }
                        });
                    }

                    timeSlot.appendChild(daySlot);
                }

                grid.appendChild(timeSlot);
            }
        }

        function renderDayView() {
            const content = document.getElementById('day-calendar-content');
            content.innerHTML = '';

            const dateStr = currentDate.toISOString().split('T')[0];
            const dayData = calendarData[dateStr];

            if (!dayData || dayData.total_count === 0) {
                content.innerHTML = `
                    <div class="no-events">
                        <i class="fas fa-calendar-day"></i>
                        <p>No events for this day</p>
                        <button class="btn btn-primary" onclick="openQuickAddModal('${dateStr}')">
                            <i class="fas fa-plus"></i> Add Event
                        </button>
                    </div>
                `;
                return;
            }

            // Journal entries
            if (dayData.entries.length > 0) {
                const entriesSection = document.createElement('div');
                entriesSection.className = 'day-section';
                entriesSection.innerHTML = `
                    <h4><i class="fas fa-book"></i> Journal Entries (${dayData.entries.length})</h4>
                `;

                dayData.entries.forEach(entry => {
                    const entryEl = document.createElement('div');
                    entryEl.className = 'day-event entry-event';
                    entryEl.innerHTML = `
                        <div class="event-time">${entry.created_at}</div>
                        <div class="event-title">${entry.title}</div>
                        <div class="event-content">${entry.content}</div>
                        ${entry.tags.length > 0 ? `<div class="event-tags">${entry.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>` : ''}
                    `;
                    entryEl.addEventListener('click', () => showEventDetails(entry));
                    entriesSection.appendChild(entryEl);
                });

                content.appendChild(entriesSection);
            }

            // Reminders
            if (dayData.reminders.length > 0) {
                const remindersSection = document.createElement('div');
                remindersSection.className = 'day-section';
                remindersSection.innerHTML = `
                    <h4><i class="fas fa-bell"></i> Reminders (${dayData.reminders.length})</h4>
                `;

                dayData.reminders.forEach(reminder => {
                    const reminderEl = document.createElement('div');
                    reminderEl.className = `day-event reminder-event ${reminder.is_completed ? 'completed' : ''}`;
                    reminderEl.innerHTML = `
                        <div class="event-time">${reminder.time}</div>
                        <div class="event-title">${reminder.title}</div>
                        ${reminder.description ? `<div class="event-content">${reminder.description}</div>` : ''}
                        <div class="event-status">
                            <span class="status-badge ${reminder.is_completed ? 'completed' : 'pending'}">
                                ${reminder.is_completed ? 'Completed' : 'Pending'}
                            </span>
                        </div>
                    `;
                    reminderEl.addEventListener('click', () => showEventDetails(reminder));
                    remindersSection.appendChild(reminderEl);
                });

                content.appendChild(remindersSection);
            }
        }

        // Modal and event handling functions
        function setupModalControls() {
            // Close modal handlers
            document.querySelectorAll('.modal-close').forEach(btn => {
                btn.addEventListener('click', closeModals);
            });

            // Click outside modal to close
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) closeModals();
                });
            });

            // Tab switching in quick add modal
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const tab = e.target.dataset.tab;
                    switchTab(tab);
                });
            });

            // Quick save handler
            document.getElementById('quick-save-btn').addEventListener('click', handleQuickSave);
        }

        function openQuickAddModal(dateStr = null) {
            const modal = document.getElementById('quick-add-modal');
            const date = dateStr || currentDate.toISOString().split('T')[0];

            document.getElementById('entry-date').value = date;
            document.getElementById('reminder-date').value = date;

            modal.style.display = 'block';
        }

        function openSearchModal() {
            const modal = document.getElementById('search-modal');
            modal.style.display = 'block';
        }

        function closeModals() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.style.display = 'none';
            });
        }

        function switchTab(tab) {
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.tab === tab);
            });

            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.toggle('active', content.id === tab + '-tab');
            });
        }

        function showDayEvents(date, dayData) {
            if (!dayData || dayData.total_count === 0) {
                openQuickAddModal(date.toISOString().split('T')[0]);
                return;
            }

            // Switch to day view for the selected date
            currentDate = new Date(date);
            switchView('day');
        }

        function showEventDetails(event) {
            const modal = document.getElementById('event-modal');
            const title = document.getElementById('modal-title');
            const content = document.getElementById('modal-content');

            title.textContent = event.title;

            if (event.type === 'entry') {
                content.innerHTML = `
                    <div class="event-details">
                        <div class="detail-row">
                            <strong>Type:</strong> Journal Entry
                        </div>
                        <div class="detail-row">
                            <strong>Created:</strong> ${event.created_at}
                        </div>
                        <div class="detail-row">
                            <strong>Content:</strong>
                            <div class="content-preview">${event.content}</div>
                        </div>
                        ${event.tags.length > 0 ? `
                            <div class="detail-row">
                                <strong>Tags:</strong>
                                <div class="tags-list">${event.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>
                            </div>
                        ` : ''}
                    </div>
                `;
            } else {
                content.innerHTML = `
                    <div class="event-details">
                        <div class="detail-row">
                            <strong>Type:</strong> Reminder
                        </div>
                        <div class="detail-row">
                            <strong>Time:</strong> ${event.time}
                        </div>
                        <div class="detail-row">
                            <strong>Status:</strong>
                            <span class="status-badge ${event.is_completed ? 'completed' : 'pending'}">
                                ${event.is_completed ? 'Completed' : 'Pending'}
                            </span>
                        </div>
                        ${event.description ? `
                            <div class="detail-row">
                                <strong>Description:</strong>
                                <div class="content-preview">${event.description}</div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            modal.style.display = 'block';
        }

        function handleQuickSave() {
            const activeTab = document.querySelector('.tab-btn.active').dataset.tab;

            if (activeTab === 'entry') {
                // Handle journal entry creation
                const form = document.getElementById('quick-entry-form');
                const formData = new FormData(form);

                // You would typically send this to your backend
                console.log('Creating journal entry:', Object.fromEntries(formData));

                // For now, redirect to the full form
                const date = formData.get('date');
                window.location.href = `/journal/new/?date=${date}`;
            } else {
                // Handle reminder creation
                const form = document.getElementById('quick-reminder-form');
                const formData = new FormData(form);

                console.log('Creating reminder:', Object.fromEntries(formData));

                // For now, redirect to the full form
                window.location.href = '/reminders/new/';
            }
        }

        // Global functions for inline event handlers
        window.openQuickAddModal = openQuickAddModal;
        window.showEventDetails = showEventDetails;
    });
</script>
{% endblock %}





