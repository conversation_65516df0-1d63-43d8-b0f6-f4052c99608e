{% extends 'app/base.html' %}

{% block content %}
<div class="hero-section">
    <div class="hero-content">
        <h1>Welcome to Your Personal Journal</h1>
        <p>Capture your thoughts, set reminders, and organize your life in one place.</p>
        <div class="hero-buttons">
            {% if user.is_authenticated %}
            <a href="{% url 'journal_list' %}" class="button pulse-button">
                <i class="fas fa-book animated-icon"></i> My Journals
            </a>
            <a href="{% url 'calendar_view' %}" class="button">
                <i class="fas fa-calendar-alt animated-icon"></i> Calendar
            </a>
            <a href="{% url 'reminder_list' %}" class="button">
                <i class="fas fa-bell animated-icon"></i> Reminders
            </a>
            {% else %}
            <a href="{% url 'signup' %}" class="button pulse-button">
                <i class="fas fa-user-plus animated-icon"></i> Sign Up
            </a>
            <a href="{% url 'login' %}" class="button">
                <i class="fas fa-sign-in-alt animated-icon"></i> Log In
            </a>
            {% endif %}
        </div>
    </div>
</div>

<style>
/* Additional styles for the landing page */
[data-theme="dark"] .hero-section {
    background-color: var(--dark-purple);
    padding: 80px 20px;
    text-align: center;
    border-radius: 0 0 20px 20px;
    margin-bottom: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .hero-content {
    max-width: 800px;
    margin: 0 auto;
}

[data-theme="dark"] .hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--bright-orange);
}

[data-theme="dark"] .hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: var(--light-beige);
}

[data-theme="dark"] .hero-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

@media (max-width: 768px) {
    [data-theme="dark"] .hero-content h1 {
        font-size: 2rem;
    }
    
    [data-theme="dark"] .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
}
</style>
{% endblock %}






