# Journal Application

A personal journaling application built with Django and MongoDB.

## Setup Instructions

### 1. Install MongoDB

First, install MongoDB on your system:

- **Windows**: Download and install from [MongoDB Download Center](https://www.mongodb.com/try/download/community)
- **macOS**: Use Homebrew: `brew install mongodb-community`
- **Linux**: Follow the [MongoDB installation instructions](https://docs.mongodb.com/manual/administration/install-on-linux/)

### 2. Start MongoDB

Start the MongoDB service:

- **Windows**: MongoDB should run as a service after installation
- **macOS**: `brew services start mongodb-community`
- **Linux**: `sudo systemctl start mongod`

### 3. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 4. Initialize MongoDB

Run the initialization script to set up the required collections and indexes:

```bash
python scripts/init_mongodb.py
```

### 5. Configure MongoDB Connection

If your MongoDB requires authentication, update the settings in `project/settings.py`:

```python
DATABASES = {
    'default': {
        'ENGINE': 'djongo',
        'NAME': 'journal_db',
        'ENFORCE_SCHEMA': False,
        'CLIENT': {
            'host': 'mongodb://localhost:27017',
            'port': 27017,
            'username': 'your_username',  # Add your MongoDB username
            'password': 'your_password',  # Add your MongoDB password
            'authSource': 'admin',
        }
    }
}
```

### 6. Run Migrations

```bash
python manage.py migrate
```

### 7. Start the Development Server

```bash
python manage.py runserver
```

## Using the Application

1. Create an account using the signup page
2. Log in with your credentials
3. Start creating journal entries
4. Use the calendar view to see entries by date
5. Search for specific entries using the search function

## MongoDB Collections

The application uses the following MongoDB collections:

- **users**: User information linked to Django authentication
- **journal_entries**: All journal entries with content and metadata
- **journal_streaks**: Tracking user streaks for consistent journaling
- **journal_reminders**: User-configured reminders for journaling